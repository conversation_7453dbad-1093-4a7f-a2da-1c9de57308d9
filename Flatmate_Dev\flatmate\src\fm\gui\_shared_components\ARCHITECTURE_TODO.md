# Shared Components Architecture TODO
# TODO: Refactor to match ideal structure
## Current Structure Issues

The current folder structure has evolved organically and has some inconsistencies:

```
_shared_components/
├── base/                    # Panel/Pane base classes only
├── widgets/                 # ALL base widgets (should be in base/)
├── toolbar/                 # ALL base toolbar components (should be in base/)
├── table_view_v2/          # Complete reusable component
└── [other folders]/
```

## Ideal Structure

For consistency and clarity, ALL base classes should be in the `base/` folder:

```
_shared_components/
├── base/                    # ALL base classes
│   ├── panels/             # Panel/Pane base classes
│   ├── widgets/            # Base widget classes
│   ├── toolbars/           # Base toolbar classes
│   └── [other_bases]/
├── table_view_v2/          # Complete reusable component
├── [other_reusable_components]/
└── utils/                  # Shared utilities
```

## Why This Refactoring is Needed

1. **Clarity**: Developers would immediately know where to find base classes
2. **Consistency**: All base classes in one logical location
3. **Maintainability**: Easier to manage dependencies and inheritance
4. **Industry Standard**: Most frameworks organize base classes this way

## Why We Can't Do This Now

- **Breaking Changes**: Would require updating imports across the entire codebase
- **Risk**: High chance of introducing bugs during the refactoring
- **Time**: Would be a significant undertaking requiring careful planning
- **Testing**: Would need comprehensive testing to ensure nothing breaks

## Recommendation

- **Document this as technical debt**
- **Plan for future refactoring** when we have better test coverage
- **For now**: Continue with current structure but be aware of the inconsistency
- **New components**: Consider where they logically belong vs. where they need to go for consistency

## Current Workaround

For the toolbar refactoring, we'll:
1. Keep base toolbar components in `_shared_components/toolbar/` (current location)
2. Keep table-specific toolbar components in `table_view_v2/components/toolbars/`
3. Document this inconsistency for future resolution

---
*Created: 2025-01-20*  
*Status: Technical Debt - Future Refactoring Needed*
