#!/usr/bin/env python3
"""
Debug test for filter issues.
"""

import sys
import os
import pandas as pd

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

def test_column_selector():
    """Test the column selector behavior."""
    print("=== Testing Column Selector ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from fm.gui._shared_components.table_view_v2.components.toolbar.groups.filter_group import ColumnSelector
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create column selector
        selector = ColumnSelector()
        
        # Test with typical categorize columns
        columns = ['date', 'details', 'amount', 'account', 'tags']
        column_names = {
            'date': 'Date',
            'details': 'Details', 
            'amount': 'Amount',
            'account': 'Account',
            'tags': 'Tags'
        }
        
        print(f"Setting columns: {columns}")
        print(f"Column names mapping: {column_names}")
        
        selector.set_columns(columns, column_names)
        
        # Check what was set
        print(f"\nColumn selector contents:")
        for i in range(selector.count()):
            text = selector.itemText(i)
            data = selector.itemData(i)
            print(f"  Index {i}: '{text}' -> '{data}'")
        
        # Check current selection
        current_data = selector.currentData()
        current_text = selector.currentText()
        print(f"\nCurrent selection: '{current_text}' -> '{current_data}'")
        
        # Test setting to details
        print(f"\nTesting set_selected_column('details')...")
        selector.set_selected_column('details')
        
        new_data = selector.currentData()
        new_text = selector.currentText()
        print(f"After setting to details: '{new_text}' -> '{new_data}'")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_config():
    """Test table config defaults."""
    print("\n=== Testing Table Config ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_config_v2 import TableConfig
        
        config = TableConfig()
        
        print(f"save_filter_state: {config.save_filter_state}")
        print(f"default_filter_column: {config.default_filter_column}")
        print(f"last_filter_column: {config.last_filter_column}")
        print(f"last_filter_pattern: {config.last_filter_pattern}")
        
        # Test with saved state
        config.last_filter_column = "details"
        config.last_filter_pattern = "Coffee"
        
        print(f"\nAfter setting saved state:")
        print(f"last_filter_column: {config.last_filter_column}")
        print(f"last_filter_pattern: {config.last_filter_pattern}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_filter_pattern_parsing():
    """Test the filter pattern parsing."""
    print("\n=== Testing Filter Pattern Parsing ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        test_cases = [
            "Coffee Shop",
            "Coffee -Refund", 
            "-exclude",
            "nbc OR",  # This was mentioned as problematic
            ""
        ]
        
        for pattern in test_cases:
            try:
                and_terms, exclude_terms = proxy._parse_filter_pattern(pattern)
                print(f"Pattern '{pattern}' -> AND: {and_terms}, EXCLUDE: {exclude_terms}")
                
                # Test matching
                test_data = "Coffee Shop Purchase"
                result = proxy._check_pattern_match(test_data, pattern)
                print(f"  Matches '{test_data}': {result}")
                
            except Exception as e:
                print(f"  Error with pattern '{pattern}': {e}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debug tests."""
    print("=== Filter Debug Tests ===\n")
    
    tests = [
        ("Column Selector", test_column_selector),
        ("Table Config", test_table_config),
        ("Filter Pattern Parsing", test_filter_pattern_parsing)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print("=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
