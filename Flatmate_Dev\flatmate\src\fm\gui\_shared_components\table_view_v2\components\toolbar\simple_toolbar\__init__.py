"""
Simple Toolbar Implementation

A clean, working implementation of the table view toolbar that addresses
the user requirements:

1. New layout: [Column] [Search] "in:" [Dropdown] [Export]
2. Internal DB columns filtered out
3. Smart search logic imported from existing FilterGroup
4. Easy layout tweaking and maintenance

This implementation is designed to be:
- Simple and maintainable
- Easy to modify and extend
- Performance optimized
- Drop-in replacement compatible

Usage:
    from .simple_toolbar import TableViewToolbarSimple
    
    toolbar = TableViewToolbarSimple()
    toolbar.set_columns(columns, column_names)
    toolbar.filter_applied.connect(your_handler)
"""

from .table_view_toolbar_simple import TableViewToolbarSimple, create_simple_toolbar

__all__ = [
    'TableViewToolbarSimple',
    'create_simple_toolbar'
]
