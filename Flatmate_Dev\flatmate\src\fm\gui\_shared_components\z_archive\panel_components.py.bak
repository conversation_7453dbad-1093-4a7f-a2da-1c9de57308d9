"""
Panel Components

Provides reusable panel components for the application's UI.
These components follow the application's design patterns and maintain
clean architectural boundaries.
"""

from PySide6.QtCore import Signal, Qt, QSize
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QScrollArea, QSizePolicy
)
from PySide6.QtGui import QIcon

# Import panel state enum
from .panels.panel_state import PanelState

# Import logger for debug logging
from ...core.services.logger import log


class IconButton(QPushButton):
    """
    A button with an icon and optional tooltip.
    
    This component is used for navigation and action buttons in panel sidebars.
    """
    
    def __init__(self, icon_path, tooltip=None, parent=None):
        """
        Initialize the icon button.
        
        Args:
            icon_path: Path to the icon file
            tooltip: Optional tooltip text
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Set up the button appearance
        self.setIcon(QIcon(icon_path))
        self.setIconSize(QSize(24, 24))
        self.setFixedSize(40, 40)
        self.setObjectName("panel_icon_button")
        
        if tooltip:
            self.setToolTip(tooltip)
        
        # Remove border and background
        self.setFlat(True)


class NavPane(QWidget):
    """
    Navigation pane component for the right panel.
    
    Contains icon buttons for navigating between different modules.
    
    Signals:
        module_selected: Emitted when a module is selected
    """
    
    module_selected = Signal(str)
    
    def __init__(self, parent=None):
        """Initialize the navigation pane."""
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 10, 0, 10)
        layout.setSpacing(15)
        layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignHCenter)
        
        # Add placeholder for module icons
        # These will be populated by the module that uses this component
        
        # Set fixed width for the collapsed state
        self.setFixedWidth(60)
    
    def add_module_button(self, module_id, icon_path, tooltip=None):
        """
        Add a module button to the navigation pane.
        
        Args:
            module_id: Identifier for the module
            icon_path: Path to the icon file
            tooltip: Optional tooltip text
        """
        button = IconButton(icon_path, tooltip, self)
        button.clicked.connect(lambda: self.module_selected.emit(module_id))
        self.layout().addWidget(button)
        return button


class SettingsPane(QWidget):
    """
    Settings pane component for the right panel.
    
    Contains a gear icon that expands to show settings options.
    
    Signals:
        settings_toggled: Emitted when the settings button is clicked
    """
    
    settings_toggled = Signal()
    
    def __init__(self, parent=None):
        """Initialize the settings pane."""
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 10, 0, 10)
        layout.setSpacing(0)
        layout.setAlignment(Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignHCenter)
        
        # Gear icon button
        self.gear_button = QPushButton(self)
        self.gear_button.setFlat(True)
        self.gear_button.setFixedSize(40, 40)
        self.gear_button.setObjectName("gear_button")
        self.gear_button.setToolTip("Settings (*panel_components.py:126)")
        self.gear_button.clicked.connect(self.settings_toggled.emit)
        
        layout.addWidget(self.gear_button)
        
        # Set fixed width for the collapsed state
        self.setFixedWidth(60)
    
    def set_gear_icon(self, icon_path):
        """
        Set the gear icon.
        
        Args:
            icon_path: Path to the icon file
        """
        self.gear_button.setIcon(QIcon(icon_path))
        self.gear_button.setIconSize(QSize(24, 24))


class ExpandablePanel(QWidget):
    """
    Expandable panel component.
    
    This panel can expand and collapse to show/hide its contents.
    """
    
    def __init__(self, parent=None):
        """Initialize the expandable panel."""
        super().__init__(parent)
        self._expanded = False
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self._layout = QVBoxLayout(self)
        self._layout.setContentsMargins(10, 10, 10, 10)
        self._layout.setSpacing(10)
        
        # Content area
        self.content_area = QScrollArea()
        self.content_area.setWidgetResizable(True)
        self.content_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # Content widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(10)
        self.content_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        self.content_area.setWidget(self.content_widget)
        self._layout.addWidget(self.content_area)
        
        # Initially collapsed
        self.setFixedWidth(0)
        self.hide()
    
    def expand(self, width=240):
        """
        Expand the panel.
        
        Args:
            width: Width to expand to
        """
        self.show()
        self.setFixedWidth(width)
        self._expanded = True
    
    def collapse(self):
        """Collapse the panel."""
        self.hide()
        self.setFixedWidth(0)
        self._expanded = False
    
    def is_expanded(self):
        """
        Check if the panel is expanded.
        
        Returns:
            bool: True if expanded, False otherwise
        """
        return self._expanded
    
    def add_widget(self, widget):
        """
        Add a widget to the panel.
        
        Args:
            widget: Widget to add
        """
        self.content_layout.addWidget(widget)


class RightPanelManager(QWidget):
    """
    Manager for the right panel components.
    
    Handles the layout and interaction between the navigation pane,
    settings pane, and expandable settings panel.
    
    Supports compact mode (icons only) and expanded mode (with settings panel).
    """
    
    module_selected = Signal(str)
    
    def __init__(self, parent=None):
        """Initialize the right panel manager."""
        super().__init__(parent)
        self._panel_state = PanelState.COMPACT  # Default to compact mode
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Left side - Icon bar
        icon_bar = QWidget()
        icon_layout = QVBoxLayout(icon_bar)
        icon_layout.setContentsMargins(0, 0, 0, 0)
        icon_layout.setSpacing(0)
        
        # Navigation pane (top)
        self.nav_pane = NavPane()
        icon_layout.addWidget(self.nav_pane)
        
        # Settings pane (bottom)
        self.settings_pane = SettingsPane()
        icon_layout.addWidget(self.settings_pane)
        
        layout.addWidget(icon_bar)
        
        # Right side - Expandable settings panel
        self.settings_panel = ExpandablePanel()
        layout.addWidget(self.settings_panel)
        
        # Set size policy
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)
        
        # Set initial width based on compact mode
        self.setFixedWidth(60)  # Width of icon bar only
    
    def _connect_signals(self):
        """Connect widget signals."""
        # Forward module selection signal
        self.nav_pane.module_selected.connect(self.module_selected.emit)
        
        # Toggle settings panel when gear icon is clicked
        self.settings_pane.settings_toggled.connect(self._toggle_settings_panel)
    
    def _toggle_settings_panel(self):
        """Toggle the settings panel between expanded and collapsed states."""
        if self.settings_panel.is_expanded():
            self._collapse_settings_panel()
        else:
            self._expand_settings_panel()
    
    def _expand_settings_panel(self):
        """Expand the settings panel."""
        self.settings_panel.expand()
        self.setFixedWidth(60 + 240)  # Icon bar + settings panel
        self._panel_state = PanelState.EXPANDED
    
    def _collapse_settings_panel(self):
        """Collapse the settings panel."""
        self.settings_panel.collapse()
        self.setFixedWidth(60)  # Icon bar only
        self._panel_state = PanelState.COMPACT
    
    def add_module_button(self, module_id, icon_path, tooltip=None):
        """
        Add a module button to the navigation pane.
        
        Args:
            module_id: Identifier for the module
            icon_path: Path to the icon file
            tooltip: Optional tooltip text
        """
        return self.nav_pane.add_module_button(module_id, icon_path, tooltip)
    
    def set_gear_icon(self, icon_path):
        """
        Set the gear icon.
        
        Args:
            icon_path: Path to the icon file
        """
        self.settings_pane.set_gear_icon(icon_path)
    
    def add_settings_widget(self, widget):
        """
        Add a widget to the settings panel.
        
        Args:
            widget: Widget to add
        """
        self.settings_panel.add_widget(widget)
    
    def set_panel_state(self, state):
        """
        Set the panel state to compact or expanded.
        
        Args:
            state: 'compact' or 'expanded'
        """
        if isinstance(state, str):
            try:
                state = PanelState.from_string(state)
            except ValueError:
                log.warning(f"Invalid panel state: {state}")
                return
        
        if state == PanelState.COMPACT:
            self._collapse_settings_panel()
        elif state == PanelState.EXPANDED:
            self._expand_settings_panel()
        elif state == PanelState.HIDDEN:
            self.hide()
        else:
            log.warning(f"Unsupported panel state: {state}")
    
    def get_panel_state(self):
        """
        Get the current panel state.
        
        Returns:
            PanelState: Current panel state
        """
        return self._panel_state
