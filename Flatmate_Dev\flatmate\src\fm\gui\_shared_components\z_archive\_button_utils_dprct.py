"""
Utility functions for button-related operations in the FlatMate application.
"""

from PySide6.QtWidgets import QPushButton
from typing import Optional, Callable

def set_button_enabled_state(
    button: QPushButton, 
    is_enabled: bool, 
    disabled_tooltip: Optional[str] = None
) -> None:
    """
    Utility to set button enabled/disabled state with optional tooltip.
    
    Args:
        button: The QPushButton to modify
        is_enabled: Whether the button should be enabled
        disabled_tooltip: Optional tooltip to show when button is disabled
    """
    button.setEnabled(is_enabled)
    
    if not is_enabled and disabled_tooltip:
        button.setToolTip(disabled_tooltip)

def connect_button_with_validation(
    button: QPushButton, 
    action: Callable, 
    validation_func: Optional[Callable[[], bool]] = None
) -> None:
    """
    Connect a button to an action with optional pre-action validation.
    
    Args:
        button: The QPushButton to connect
        action: The main action to perform when button is clicked
        validation_func: Optional function to validate before action
    """
    def validated_action():
        if validation_func is None or validation_func():
            action()
    
    button.clicked.connect(validated_action)
