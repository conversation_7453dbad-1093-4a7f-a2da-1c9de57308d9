"""
Test script to verify the new toolbar structure is working correctly.

This script tests:
1. Import functionality
2. Toolbar instantiation
3. Basic signal connectivity
4. Column setting functionality
"""

import sys
from pathlib import Path

# Add the src directory to Python path for imports
src_path = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(src_path))

def test_toolbar_imports():
    """Test that both toolbars can be imported successfully."""
    print("Testing toolbar imports...")
    
    try:
        from src.fm.gui._shared_components.table_view_v2.components.toolbars import (
            TableViewToolbar, 
            TableViewToolbarLegacy,
            FilterGroup,
            ColumnGroup, 
            ExportGroup
        )
        print("✅ All imports successful")
        print(f"   Default toolbar: {TableViewToolbar.__name__}")
        print(f"   Legacy toolbar: {TableViewToolbarLegacy.__name__}")
        print(f"   Components: {FilterGroup.__name__}, {ColumnGroup.__name__}, {ExportGroup.__name__}")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_toolbar_instantiation():
    """Test that toolbars can be instantiated."""
    print("\nTesting toolbar instantiation...")
    
    try:
        from src.fm.gui._shared_components.table_view_v2.components.toolbars import (
            TableViewToolbar, 
            TableViewToolbarLegacy
        )
        
        # Test default toolbar
        default_toolbar = TableViewToolbar()
        print(f"✅ Default toolbar created: {type(default_toolbar).__name__}")
        
        # Test legacy toolbar  
        legacy_toolbar = TableViewToolbarLegacy()
        print(f"✅ Legacy toolbar created: {type(legacy_toolbar).__name__}")
        
        return True
    except Exception as e:
        print(f"❌ Instantiation failed: {e}")
        return False

def test_toolbar_signals():
    """Test that toolbar signals are properly defined."""
    print("\nTesting toolbar signals...")
    
    try:
        from src.fm.gui._shared_components.table_view_v2.components.toolbars import TableViewToolbar
        
        toolbar = TableViewToolbar()
        
        # Check for expected signals
        expected_signals = [
            'filter_applied',
            'filters_cleared', 
            'column_visibility_requested',
            'csv_export_requested',
            'excel_export_requested'
        ]
        
        for signal_name in expected_signals:
            if hasattr(toolbar, signal_name):
                print(f"✅ Signal found: {signal_name}")
            else:
                print(f"❌ Signal missing: {signal_name}")
                return False
                
        return True
    except Exception as e:
        print(f"❌ Signal test failed: {e}")
        return False

def test_column_setting():
    """Test that columns can be set on the toolbar."""
    print("\nTesting column setting...")
    
    try:
        from src.fm.gui._shared_components.table_view_v2.components.toolbars import TableViewToolbar
        
        toolbar = TableViewToolbar()
        
        # Test columns
        test_columns = ['id', 'name', 'email', 'status', 'db_uid', '_internal']
        column_names = {
            'id': 'ID',
            'name': 'Full Name', 
            'email': 'Email Address',
            'status': 'Status'
        }
        
        # This should not raise an exception
        toolbar.set_columns(test_columns, column_names)
        print("✅ Columns set successfully")
        
        # Test getting current state
        if hasattr(toolbar, 'get_filter_state'):
            state = toolbar.get_filter_state()
            print(f"✅ Filter state retrieved: {type(state)}")
        
        return True
    except Exception as e:
        print(f"❌ Column setting failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing New Toolbar Structure")
    print("=" * 50)
    
    tests = [
        test_toolbar_imports,
        test_toolbar_instantiation, 
        test_toolbar_signals,
        test_column_setting
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Toolbar structure is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    # Only run if we have a GUI environment
    try:
        from PySide6.QtWidgets import QApplication
        import sys
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        success = main()
        sys.exit(0 if success else 1)
        
    except ImportError:
        print("❌ PySide6 not available - cannot test GUI components")
        sys.exit(1)
