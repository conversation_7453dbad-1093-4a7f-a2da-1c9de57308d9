"""
Table View Infrastructure for the Flatmate Application

This package provides core table infrastructure with advanced features for
displaying, filtering, and interacting with tabular data.

Main Components:
- EnhancedTableView: Core table view with advanced features
- EnhancedTableWidget: Complete table solution with toolbar and controls
- EnhancedTableModel: Data storage with editable/readonly column support
- EnhancedFilterProxyModel: Filtering and sorting logic

Usage:
    # Complete table widget with toolbar
    from fm.gui.shared.table_views import EnhancedTableWidget
    
    # Just the table view
    from fm.gui.shared.table_views import EnhancedTableView
    
    # Individual components for custom solutions
    from fm.gui.shared.table_views import EnhancedTableModel, EnhancedFilterProxyModel
"""

from .fm_table_view import CustomTableView
from .components.table_view_core import TableViewCore


__all__ = [
    'CustomTableView',  
    'TableViewCore',]
