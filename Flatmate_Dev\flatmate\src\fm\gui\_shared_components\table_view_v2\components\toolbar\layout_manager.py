"""
Layout Manager - Optimized Toolbar Layout System

Manages the layout and positioning of toolbar components according to the new specifications.
"""

from typing import Dict, Any, Optional
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QHBoxLayout, QWidget, QSpacerItem, QSizePolicy, QLabel


class ToolbarLayoutManager:
    """Manages the layout and positioning of toolbar components."""
    
    def __init__(self):
        self._layout = None
        self._widgets = {}
        self._spacers = {}
    
    def setup_layout(self, parent: QWidget) -> QHBoxLayout:
        """Set up the optimized toolbar layout."""
        layout = QHBoxLayout(parent)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(8)  # Reduced spacing for tighter layout
        
        self._layout = layout
        self._widgets = {}
        self._spacers = {}
        
        return layout
    
    def add_visible_columns_selector(self, widget: QWidget):
        """Add visible columns selector to the left side."""
        self._widgets['visible_columns'] = widget
        self._layout.addWidget(widget)
    
    def add_search_textbox(self, widget: QWidget):
        """Add search textbox with expand policy."""
        self._widgets['search_textbox'] = widget
        widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self._layout.addWidget(widget)
    
    def add_in_label(self):
        """Add 'in:' label."""
        label = QLabel("in:")
        label.setObjectName("inLabel")
        self._widgets['in_label'] = label
        self._layout.addWidget(label)
    
    def add_search_column_selector(self, widget: QWidget):
        """Add search column selector with size adjustment."""
        self._widgets['search_column'] = widget
        widget.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Preferred)
        self._layout.addWidget(widget)
    
    def add_export_button(self, widget: QWidget):
        """Add export button to the right side."""
        self._widgets['export_button'] = widget
        self._layout.addWidget(widget)
    
    def get_layout(self) -> QHBoxLayout:
        """Get the configured layout."""
        return self._layout
    
    def get_widget(self, name: str) -> Optional[QWidget]:
        """Get a widget by name."""
        return self._widgets.get(name)
    
    def update_layout(self):
        """Update layout after changes."""
        if self._layout:
            self._layout.update()