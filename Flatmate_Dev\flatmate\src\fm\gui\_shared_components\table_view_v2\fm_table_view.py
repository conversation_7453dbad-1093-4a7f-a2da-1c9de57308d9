"""
Enhanced Table View v2 - Complete Table Solution

This is the main entry point for the enhanced table system v2. It provides a complete
table widget with filtering, column management, and export capabilities following
the app-wide widget pattern.

Features:
- configure() method for instance runtime defaults
- Dynamic methods for runtime changes
- Chainable API for fluent configuration
- No competing override chains
"""

from typing import Dict, List, Union, Optional
import pandas as pd

from PySide6.QtCore import Qt
from PySide6.QtGui import QAction
from PySide6.QtWidgets import QWidget, QVBoxLayout, QFrame, QMenu

from .components.table_view_core import TableViewCore
from .components.toolbars import TableViewToolbar
from .components.table_config_v2 import TableConfig
from fm.core.data_services.standards.columns import Columns


class CustomTableView_v2(QWidget):
    """
    Complete table solution v2 following app-wide widget pattern.

    Features:
    - configure() method for instance runtime defaults
    - Dynamic methods for runtime changes (hide_columns, resize_column, etc.)
    - Chainable API for fluent configuration
    - No competing override chains
    - Optional toolbar

    Usage:
        # Basic usage
        table = CustomTableView_v2()
        table.set_dataframe(df).show()

        # Configured usage
        table = CustomTableView_v2()
        table.configure(
            auto_size_columns=True,
            max_column_width=40,
            editable_columns=['tags'],
            show_toolbar=True
        ).set_dataframe(df).show()

        # Dynamic changes
        table.hide_columns(['balance']).resize_column('details', 60)
    """

    def __init__(self, parent=None):
        """Initialize with sensible defaults."""
        super().__init__(parent)

        # Configuration and state
        self._config = TableConfig()
        self._dataframe = None
        self._is_shown = False

        # UI components (will be created in _setup_ui)
        self.toolbar = None
        self.table_view = None
        self._separator = None

        # Set up UI
        self._setup_ui()

    def _setup_ui(self):
        """Set up the UI components."""
        # Main layout
        self._layout = QVBoxLayout(self)
        self._layout.setContentsMargins(0, 0, 0, 0)
        self._layout.setSpacing(2)  # Minimal spacing between toolbar and table

        # Toolbar component (initially visible based on config)
        self.toolbar = TableViewToolbar()
        self._layout.addWidget(self.toolbar)

        # Separator
        self._separator = QFrame()
        self._separator.setFrameShape(QFrame.Shape.HLine)
        self._separator.setFrameShadow(QFrame.Shadow.Sunken)
        self._layout.addWidget(self._separator)

        # Core table view (the actual table)
        self.table_view = TableViewCore()
        self._layout.addWidget(self.table_view, 1)  # Give table stretch priority

        # Connect toolbar signals to table functionality
        self._connect_signals()

        # Apply initial configuration
        self._apply_toolbar_visibility()

    # === CORE PATTERN METHODS ===

    def configure(self, **kwargs) -> 'CustomTableView_v2':
        """Configure table behavior and appearance.

        This sets the instance runtime defaults for this table.

        Args:
            **kwargs: Configuration options. See TableConfig for all available options.

        Common options:
            auto_size_columns (bool): Automatically size columns to content
            max_column_width (int): Maximum column width in characters
            column_widths (Dict[str, int]): Explicit column widths
            editable_columns (List[str]): Columns that can be edited
            default_visible_columns (List[str]): Columns to show by default
            show_toolbar (bool): Whether to show the toolbar
            column_display_mapping (Dict[str, str]): Map db_names to display_names

        Returns:
            self for method chaining

        Example:
            table.configure(
                auto_size_columns=True,
                max_column_width=40,
                editable_columns=['tags'],
                show_toolbar=True
            )
        """
        # Update config with provided kwargs
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
            else:
                raise ValueError(f"Unknown configuration option: {key}. See TableConfig for valid options.")

        # Validate configuration
        self._config.validate()

        # Apply configuration if already shown
        if self._is_shown and self._dataframe is not None:
            self._apply_configuration()

        return self

    def set_dataframe(self, df: pd.DataFrame, custom_column_names: Optional[Dict[str, str]] = None) -> 'CustomTableView_v2':
        """Set the table data from a pandas DataFrame with automatic column name handling.

        Args:
            df: DataFrame with db_names as columns (from database)
            custom_column_names: Optional custom db_name -> display_name mapping

        Returns:
            self for method chaining

        The table view handles all column name conversion internally.
        """
        if df is None:
            df = pd.DataFrame()

        self._dataframe_original = df.copy()

        # Apply display names using the centralized Columns class
        self._dataframe = Columns.apply_display_names_to_df(df, custom_mapping=custom_column_names)

        # Store the mapping in the config
        self._config.column_display_mapping = {col: self._dataframe.columns[i] for i, col in enumerate(df.columns) if col in self._dataframe.columns}

        # Store mapping for internal use and configuration
        self._column_mapping = {col: self._dataframe.columns[i] for i, col in enumerate(df.columns) if col in self._dataframe.columns}
        self._config.column_display_mapping = {col: self._dataframe.columns[i] for i, col in enumerate(df.columns) if col in self._dataframe.columns}

        # Apply configuration if already shown
        if self._is_shown:
            self._apply_configuration()

        return self

    def show(self) -> 'CustomTableView_v2':
        """Show the table and apply all configuration.

        This triggers the display and applies all pending configuration.

        Returns:
            self for method chaining
        """
        self._is_shown = True

        if self._dataframe is not None:
            self._apply_configuration()

        super().show()
        return self

    def hide(self) -> 'CustomTableView_v2':
        """Hide the table.

        Returns:
            self for method chaining
        """
        self._is_shown = False
        super().hide()
        return self

    # === DYNAMIC RUNTIME METHODS ===

    def set_visible_columns(self, columns: List[str]) -> 'CustomTableView_v2':
        """Set which columns are visible.

        Args:
            columns: List of column names to make visible

        Returns:
            self for method chaining
        """
        self._config.default_visible_columns = columns.copy()
        if self._is_shown:
            self._apply_column_visibility()
        return self

    def hide_columns(self, columns: List[str]) -> 'CustomTableView_v2':
        """Hide specific columns.

        Args:
            columns: List of column names to hide

        Returns:
            self for method chaining
        """
        if self._config.default_visible_columns is None:
            # If no visible columns set, start with all columns
            if self._dataframe is not None:
                self._config.default_visible_columns = list(self._dataframe.columns)
            else:
                return self

        # Remove specified columns from visible list
        self._config.default_visible_columns = [
            col for col in self._config.default_visible_columns
            if col not in columns
        ]

        if self._is_shown:
            self._apply_column_visibility()
        return self

    def show_columns(self, columns: List[str]) -> 'CustomTableView_v2':
        """Show specific columns.

        Args:
            columns: List of column names to show

        Returns:
            self for method chaining
        """
        if self._config.default_visible_columns is None:
            self._config.default_visible_columns = columns.copy()
        else:
            # Add columns to visible list (avoid duplicates)
            for col in columns:
                if col not in self._config.default_visible_columns:
                    self._config.default_visible_columns.append(col)

        if self._is_shown:
            self._apply_column_visibility()
        return self

    def resize_column(self, column: str, width: int) -> 'CustomTableView_v2':
        """Resize a specific column.

        Args:
            column: Column name to resize
            width: New width in characters

        Returns:
            self for method chaining
        """
        self._config.column_widths[column] = width
        if self._is_shown:
            self._apply_column_width(column, width)
        return self

    def auto_resize_columns(self) -> 'CustomTableView_v2':
        """Re-trigger auto-sizing for all columns.

        Returns:
            self for method chaining
        """
        if self._is_shown:
            self._apply_auto_sizing()
        return self

    def show_toolbar(self) -> 'CustomTableView_v2':
        """Show the toolbar.

        Returns:
            self for method chaining
        """
        self._config.show_toolbar = True
        if self._is_shown:
            self._apply_toolbar_visibility()
        return self

    def hide_toolbar(self) -> 'CustomTableView_v2':
        """Hide the toolbar.

        Returns:
            self for method chaining
        """
        self._config.show_toolbar = False
        if self._is_shown:
            self._apply_toolbar_visibility()
        return self

    def set_default_visible_columns_for_module(self, module_name: str) -> 'CustomTableView_v2':
        """
        Set default visible columns using the centralized Columns class.

        This is a convenience method to avoid hard-coding column names and
        ensure consistency across the application.

        Args:
            module_name: Name of the module ('categorize', 'reports', etc.)

        Returns:
            self for method chaining

        Example:
            table.set_default_visible_columns_for_module('categorize')
        """
        self._config.set_default_visible_columns_for_module(module_name)
        if self._is_shown:
            self._apply_column_visibility()
        return self

    def _connect_signals(self):
        """Connect toolbar signals to table functionality."""
        # Connect filter signals
        self.toolbar.filter_applied.connect(self._on_filter_applied)
        self.toolbar.filters_cleared.connect(self._on_filters_cleared)

        # Connect action signals
        self.toolbar.column_visibility_requested.connect(self._show_column_visibility)
        self.toolbar.csv_export_requested.connect(self._export_csv)
        self.toolbar.excel_export_requested.connect(self._export_excel)

    # === INTERNAL CONFIGURATION APPLICATION ===

    def _apply_configuration(self):
        """Apply all configuration to the UI components."""
        if self._dataframe is None:
            return

        # Set the dataframe in the table view
        self.table_view.set_dataframe(self._dataframe)

        # Apply column configuration
        self._apply_column_widths()
        self._apply_column_visibility()
        self._apply_editable_columns()

        # Update toolbar
        self._update_toolbar()
        self._apply_toolbar_visibility()

        # Restore filter state if enabled (delay to ensure toolbar is fully initialized)
        if self._config.save_filter_state:
            # Use QTimer to delay restoration until after UI is fully set up
            from PySide6.QtCore import QTimer
            QTimer.singleShot(100, self._restore_filter_state)

    def _apply_column_widths(self):
        """Apply column width configuration."""
        if self._dataframe is None:
            return

        # Always apply auto-sizing with limit, even if no custom widths are set
        if self._config.auto_size_columns:
            if self._config.column_widths:
                # Use custom widths with auto-sizing for others
                self.table_view.set_column_widths(
                    self._config.column_widths,
                    max_chars=self._config.max_column_width
                )
            else:
                # Just auto-size all columns with limit
                self.table_view._auto_resize_columns_with_limit(self._config.max_column_width)
        elif self._config.column_widths:
            # Only apply custom widths without auto-sizing
            self.table_view.set_column_widths(
                self._config.column_widths,
                max_chars=self._config.max_column_width
            )

    def _apply_column_visibility(self):
        """Apply column visibility configuration."""
        if self._dataframe is None or not hasattr(self.table_view, '_model'):
            return

        all_columns = list(self._dataframe.columns)  # These are display names after conversion

        # Convert config db_names to display_names for comparison
        if self._config.default_visible_columns and hasattr(self, '_column_mapping'):
            # Convert db_names from config to display_names
            visible_display_names = []
            for db_name in self._config.default_visible_columns:
                display_name = self._column_mapping.get(db_name, db_name)
                visible_display_names.append(display_name)

            # Update config temporarily with display names for visibility check
            original_visible = self._config.default_visible_columns
            self._config.default_visible_columns = visible_display_names

            for col_idx, col_name in enumerate(all_columns):
                if col_idx < self.table_view._model.columnCount():
                    is_visible = self._config.is_column_visible(col_name, all_columns)
                    self.table_view.setColumnHidden(col_idx, not is_visible)

            # Restore original config
            self._config.default_visible_columns = original_visible
        else:
            # No default visible columns set, show all
            for col_idx, col_name in enumerate(all_columns):
                if col_idx < self.table_view._model.columnCount():
                    is_visible = self._config.is_column_visible(col_name, all_columns)
                    self.table_view.setColumnHidden(col_idx, not is_visible)

        # After changing column visibility, re-apply smart sizing to adjust Details column
        if self._config.auto_size_columns:
            self.table_view._auto_resize_columns_with_limit(self._config.max_column_width)

    def _apply_editable_columns(self):
        """Apply editable columns configuration."""
        if self._config.editable_columns:
            self.table_view.set_editable_columns(self._config.editable_columns)

    def _apply_column_width(self, column: str, width: int):
        """Apply width change to a specific column."""
        if self._dataframe is None:
            return

        # Find column index
        try:
            col_idx = list(self._dataframe.columns).index(column)
            char_width = self.table_view.fontMetrics().averageCharWidth()
            pixel_width = width * char_width
            self.table_view.horizontalHeader().resizeSection(col_idx, pixel_width)
        except ValueError:
            pass  # Column not found

    def _apply_auto_sizing(self):
        """Re-apply auto-sizing to all columns."""
        if hasattr(self.table_view, '_auto_resize_columns_with_limit'):
            self.table_view._auto_resize_columns_with_limit(self._config.max_column_width)

    def _apply_toolbar_visibility(self):
        """Apply toolbar visibility configuration."""
        if self._config.show_toolbar:
            self.toolbar.show()
            self._separator.show()
        else:
            self.toolbar.hide()
            self._separator.hide()

    def _update_toolbar(self):
        """Update toolbar with current visible columns only.

        Only visible columns should be available for search since:
        1. Users can only see and understand visible columns
        2. "All Columns" is too performance-heavy to parse
        3. Search should default to primary text column (Details)
        """
        if self._dataframe is not None:
            all_columns = list(self._dataframe.columns)

            # Get only visible columns for search dropdown
            if self._config.default_visible_columns:
                # Convert config db_names to display_names if needed
                visible_columns = []
                print(f"DEBUG: Processing visible columns: {self._config.default_visible_columns}")
                print(f"DEBUG: Available DataFrame columns: {all_columns}")
                print(f"DEBUG: Column mapping: {getattr(self, '_column_mapping', 'None')}")

                for col in self._config.default_visible_columns:
                    if hasattr(self, '_column_mapping') and col in self._column_mapping:
                        # Convert db_name to display_name
                        display_name = self._column_mapping[col]
                        if display_name in all_columns:
                            visible_columns.append(display_name)
                            print(f"DEBUG: Mapped '{col}' → '{display_name}'")
                        else:
                            print(f"DEBUG: Mapped column '{display_name}' not found in DataFrame")
                    elif col in all_columns:
                        # Already a display_name
                        visible_columns.append(col)
                        print(f"DEBUG: Direct match: '{col}'")
                    else:
                        # Try title case conversion as fallback
                        title_case = col.replace('_', ' ').title()
                        if title_case in all_columns:
                            visible_columns.append(title_case)
                            print(f"DEBUG: Title case match: '{col}' → '{title_case}'")
                        else:
                            print(f"DEBUG: Column '{col}' not found in any form")

                # Use visible columns for search options
                search_columns = visible_columns
                print(f"DEBUG: Final search columns: {search_columns}")
            else:
                # Fallback to all columns if no visible columns configured
                search_columns = all_columns
                print(f"DEBUG: Using all columns for search: {search_columns}")

            column_names = self._config.column_display_mapping
            self.toolbar.set_columns(search_columns, column_names)

            # Set default search column if configured
            if self._config.default_search_column:
                self._set_default_search_column()

    def _set_default_search_column(self):
        """Set the default search column in the toolbar."""
        if not self._config.default_search_column:
            return

        # Convert db_name to display_name if needed
        search_column = self._config.default_search_column
        if hasattr(self, '_column_mapping') and search_column in self._column_mapping:
            # Convert db_name to display_name
            display_name = self._column_mapping[search_column]
            search_column = display_name

        # Set the default in the toolbar
        if hasattr(self.toolbar, 'filter_group') and hasattr(self.toolbar.filter_group, 'column_selector'):
            selector = self.toolbar.filter_group.column_selector

            # Try to find and set the column
            index = selector.findData(search_column)
            if index >= 0:
                selector.setCurrentIndex(index)
                print(f"DEBUG: Set default search column to '{search_column}' at index {index}")
            else:
                # Try with original db_name
                index = selector.findData(self._config.default_search_column)
                if index >= 0:
                    selector.setCurrentIndex(index)
                    print(f"DEBUG: Set default search column to '{self._config.default_search_column}' at index {index}")
                else:
                    print(f"WARNING: Default search column '{self._config.default_search_column}' not found in selector")
                    # List available options for debugging
                    print("Available columns in selector:")
                    for i in range(selector.count()):
                        print(f"  {i}: '{selector.itemText(i)}' (data: '{selector.itemData(i)}')")

    # === SIGNAL PROPERTIES ===

    @property
    def row_selected(self):
        """Signal emitted when a row is selected."""
        return self.table_view.row_selected

    @property
    def cell_edited(self):
        """Signal emitted when a cell is edited."""
        return self.table_view.cell_edited

    def _on_filter_applied(self, column, pattern):
        """Handle filter applied signal from toolbar."""
        self.table_view.set_column_filter(column, pattern)
        # Save filter state for persistence
        self._save_filter_state(column, pattern)

    def _on_filters_cleared(self):
        """Handle filters cleared signal from toolbar."""
        self.table_view.clear_filters()
        # Clear saved filter state
        self._save_filter_state("", "")

    def _save_filter_state(self, column: str, pattern: str):
        """Save current filter state to config."""
        if self._config.save_filter_state:
            self._config.last_filter_column = column if column else None
            self._config.last_filter_pattern = pattern if pattern else None

    def _restore_filter_state(self):
        """Restore filter state from config."""
        print(f"DEBUG: _restore_filter_state called")
        print(f"DEBUG: save_filter_state={self._config.save_filter_state}")
        print(f"DEBUG: last_filter_pattern='{self._config.last_filter_pattern}'")
        print(f"DEBUG: last_filter_column='{self._config.last_filter_column}'")

        if (hasattr(self, 'toolbar') and
            hasattr(self.toolbar, 'filter_group')):

            if (self._config.save_filter_state and
                self._config.last_filter_pattern):
                # Restore saved filter state
                column = self._config.last_filter_column or self._config.default_filter_column
                pattern = self._config.last_filter_pattern

                print(f"DEBUG: Restoring saved filter: column='{column}', pattern='{pattern}'")

                # Set filter in UI and apply it (set_filter_state now emits the signal)
                self.toolbar.filter_group.set_filter_state(column, pattern)

            else:
                # No saved filter, but ensure default column is selected
                print(f"DEBUG: No saved filter, setting default column to '{self._config.default_filter_column}'")

                # Just set the column selector to the default, don't apply any filter
                if hasattr(self.toolbar.filter_group.column_selector, 'set_selected_column'):
                    self.toolbar.filter_group.column_selector.set_selected_column(self._config.default_filter_column)
        else:
            print("DEBUG: Filter restoration skipped - toolbar not available")

    def _on_column_visibility_toggled(self, column_idx: int, checked: bool):
        """Handle column visibility toggle from toolbar menu.

        This ensures smart resizing happens when columns are shown/hidden via the UI.
        """
        # Apply the visibility change
        self.table_view.setColumnHidden(column_idx, not checked)

        # Trigger smart resizing to adjust Details column
        if self._config.auto_size_columns:
            self.table_view._auto_resize_columns_with_limit(self._config.max_column_width)

    def _show_column_visibility(self):
        """Show column visibility menu."""
        menu = QMenu(self)

        for col in range(self.table_view._model.columnCount()):
            col_name = self.table_view._model.headerData(col, Qt.Horizontal)
            action = QAction(str(col_name), self)
            action.setCheckable(True)
            action.setChecked(not self.table_view.isColumnHidden(col))
            action.triggered.connect(lambda checked, column=col:
                                    self._on_column_visibility_toggled(column, checked))
            menu.addAction(action)

        # Show menu relative to the column group button (not far left of toolbar)
        column_group = self.toolbar.column_group
        if column_group and hasattr(column_group, 'column_visibility_button'):
            # Position menu below the column visibility button
            button_pos = column_group.column_visibility_button.mapToGlobal(
                column_group.column_visibility_button.rect().bottomLeft())
            menu.exec(button_pos)
        else:
            # Fallback to center of toolbar if button not found
            toolbar_center = self.toolbar.rect().center()
            menu.exec(self.toolbar.mapToGlobal(toolbar_center))

    def _export_csv(self):
        """Handle CSV export request."""
        self.table_view._export_data("csv")

    def _export_excel(self):
        """Handle Excel export request."""
        self.table_view._export_data("excel")

    # === BACKWARD COMPATIBILITY METHODS ===
    # These methods maintain compatibility with existing code while using the new pattern

    def get_dataframe(self) -> pd.DataFrame:
        """Get the current data as a pandas DataFrame."""
        if self.table_view:
            return self.table_view.get_dataframe()
        return pd.DataFrame()

    def set_editable_columns(self, columns: List[Union[int, str]]) -> 'CustomTableView_v2':
        """Set which columns should be editable.

        Backward compatibility method that updates configuration.

        Args:
            columns: List of column names or indices that should be editable

        Returns:
            self for method chaining
        """
        # Convert to string column names if needed
        str_columns = []
        for col in columns:
            if isinstance(col, str):
                str_columns.append(col)
            elif isinstance(col, int) and self._dataframe is not None:
                # Convert index to column name
                if 0 <= col < len(self._dataframe.columns):
                    str_columns.append(self._dataframe.columns[col])

        self._config.editable_columns = str_columns

        if self._is_shown:
            self._apply_editable_columns()

        return self

    def set_display_columns(self, columns, column_names=None) -> 'CustomTableView_v2':
        """Set which columns to display and their display names.

        Backward compatibility method that updates configuration.

        Args:
            columns: List of database column names to display
            column_names: Dictionary mapping database column names to display names

        Returns:
            self for method chaining
        """
        # Update configuration
        self._config.default_visible_columns = columns.copy() if columns else None
        if column_names:
            self._config.column_display_mapping = column_names.copy()

        # Apply if shown
        if self._is_shown:
            self._apply_column_visibility()
            self._update_toolbar()

        return self

    def set_column_widths(self, width_map: Dict[str, int]) -> 'CustomTableView_v2':
        """Set custom column widths.

        Backward compatibility method that updates configuration.

        Args:
            width_map: Dictionary mapping column names to widths (in characters)

        Returns:
            self for method chaining
        """
        # Update configuration
        self._config.column_widths.update(width_map)

        # Apply if shown
        if self._is_shown:
            self._apply_column_widths()

        return self


# Alias for backward compatibility
CustomTableView = CustomTableView_v2



