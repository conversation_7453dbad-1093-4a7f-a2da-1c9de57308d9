# implementation-protocol.md

an implementation protocol for new features or refactotring work

## Guidelines

---
description: Bulletproof protocol for implementing new features, optimized for AI systems to avoid speculative fluff and ensure concrete, actionable outcomes.
---

> **Protocol Requirement:**
> All required protocol documents must be present in the feature's implementation folder. These include: _REQUIREMENTS_prd.md, DESIGN.md, TASKS.md, IMPLEMENTATION_GUIDE.md, and _DISCUSSION.md. If any are missing, they must be created before implementation begins.

A structured, fact-based protocol for implementing new features, designed to prevent speculative fluff and ensure concrete, actionable implementation.

---

## Core Principles (Anti-Speculation Rules)

### 🚫 **NEVER DO:**
- Speculate about requirements without asking the user
- Create imaginary stakeholder perspectives or roles
- Make assumptions about user needs or business logic
- Generate vague, non-actionable tasks
- Reference files or functions without verifying they exist

### ✅ **ALWAYS DO:**
- Use `codebase-retrieval` to understand current architecture before planning
- Reference specific file paths, line numbers, and function names
- Provide complete code examples, not snippets
- Make tasks atomic and time-bounded (~20 minutes each)
- Ask for clarification when requirements are unclear

---

## Protocol Overview

**Roles:**
- **User**: Provides requirements, feedback, and final decisions (uses `>>` for input)
- **AI**: Implements based on facts, not speculation

**Required Folder Structure:**
Create folder: `flatmate/DOCS/_FEATURES/<feature_name>/`

**Required Documents:**
- `_REQUIREMENTS_prd.md` — Clear, testable requirements with acceptance criteria
- `DESIGN.md` — Architecture analysis with specific file paths and class references
- `TASKS.md` — Atomic implementation steps with code examples
- `IMPLEMENTATION_GUIDE.md` — Step-by-step technical guide
- `_DISCUSSION.md` — Decisions and progress tracking

-  *'_' prefixes are for correct sequential file tree ordering*
---

## Implementation Flow (Fact-Based)

### Step 1: **Requirements Gathering** (No Speculation)
**AI Actions:**
1. Ask user to clarify the exact desired behavior
2. Document ONLY what user explicitly states
3. Create testable acceptance criteria
4. Update `requirements.md` with facts only

**User Input Required:**
- What specific problem needs solving?
- What should the feature do exactly?
- What are the success criteria?

**Deliverable:** `requirements.md` with clear, testable requirements

---

### Step 2: **Architecture Analysis** (Fact-Based Discovery)
**AI Actions:**
1. Use `codebase-retrieval` to find relevant existing code
2. Identify specific files, classes, and methods that need modification
3. Document current implementation patterns
4. Map out exact integration points

**Required Analysis:**
- Current architecture (specific file paths)
- Affected components (exact class names)
- Integration points (method signatures)
- Dependencies (what must be done first)

**Deliverable:** `design.md` with specific technical details

---

### Step 3: **Task Breakdown** (Atomic & Specific)
**AI Actions:**
1. **MANDATORY**: Use `codebase-retrieval` to examine every file mentioned in tasks
2. Create atomic tasks (20 minutes each)
3. Include exact file paths and line numbers from actual codebase
4. Provide complete code examples (NO PLACEHOLDERS OR COMMENTS)
5. Specify dependencies between tasks
6. Include actual method signatures from codebase

**Task Requirements (ENFORCED):**
- **File Path**: Full path to file being modified (verified to exist)
- **Current Code**: Actual existing code from the file (lines X-Y)
- **New Code**: Complete implementation (not "# TODO" or comments)
- **Method Signature**: Exact signature with types from actual codebase
- **Dependencies**: What must be completed first
- **Testing**: Specific validation steps with expected outcomes

**FORBIDDEN in Tasks:**
- ❌ Placeholder comments like `# Add persistence logic here`
- ❌ Vague descriptions like `# Update method to handle...`
- ❌ References to non-existent files or methods
- ❌ Incomplete code examples

**REQUIRED in Tasks:**
- ✅ Complete, working code implementations
- ✅ Actual line numbers from codebase
- ✅ Verified file paths and method names
- ✅ Integration with existing patterns

**Deliverable:** `tasks.md` with actionable implementation steps

---

### Step 4: **Implementation Guide** (Step-by-Step)
**AI Actions:**
1. Create detailed technical guide
2. Include common pitfalls and solutions
3. Provide testing strategies
4. Document integration patterns

**Guide Contents:**
- Step-by-step implementation with code
- Error handling patterns
- Testing approaches
- Integration checkpoints

**Deliverable:** `implementation_guide.md` with technical details

--- 

### Step 5: **Implementation** (Verify Before Acting)
**AI Actions:**
1. Use `codebase-retrieval` before making any changes
2. Reference existing patterns in the codebase
3. Make changes incrementally
4. Test each change before proceeding

**Implementation Rules:**
- Always check current code before modifying
- Follow existing code patterns and conventions
- Make minimal, focused changes
- Test immediately after each change

**Deliverable:** Working implementation with tests

---

### Step 6: **Implementation & Testing** (Verify Before Completion)
**AI Actions:**
1. Implement all tasks incrementally
2. Test each component as implemented
3. Run automated tests to verify functionality
4. Document any deviations from original plan

**Implementation Requirements:**
- All tasks completed and tested
- No breaking changes introduced
- Performance acceptable
- Error cases handled

**Deliverable:** Working implementation ready for user review

#### Create change_log.md
- document all changes to the code base

#### Create <phase_n>_review.md


### Step 7: **User Review & Feedback** (Critical Quality Gate)
**User Actions:**
1. Test the implemented feature in real usage scenarios
2. Verify all acceptance criteria are met
3. Identify any issues, edge cases, or improvements needed
4. Provide feedback on usability and performance

**AI Actions:**
1. Address all user feedback promptly
2. Fix any issues identified during testing
3. Make adjustments based on user experience
4. Re-test after fixes

**Review Requirements:**
- Feature tested in actual use cases
- All user feedback addressed
- Performance meets expectations
- User experience is satisfactory

**Deliverable:** Feature approved by user for production use

---

### Step 8: **Documentation & Knowledge Transfer** (Complete the Loop)
**AI Actions:**
1. Create user-facing documentation
2. Update technical documentation
3. Document lessons learned
4. Create troubleshooting guide if needed

**Documentation Requirements:**
- User guide with examples
- Technical implementation notes
- Known limitations or considerations
- Future enhancement opportunities

**Deliverable:** Complete feature with comprehensive documentation

---

## Quality Gates (Prevent Speculation)

### Before Each Step:
- [ ] Have I used `codebase-retrieval` to understand current state?
- [ ] Am I referencing specific files and functions that actually exist?
- [ ] Are my tasks atomic and time-bounded?
- [ ] Do I have complete code examples, not just descriptions?
- [ ] Have I verified all file paths and method names exist?

### Before Task Creation (MANDATORY):
- [ ] Have I examined the actual current code using `codebase-retrieval`?
- [ ] Do I have the exact current method implementations?
- [ ] Have I identified the specific lines that need modification?
- [ ] Do I understand how the new code integrates with existing patterns?
- [ ] Are all my code examples complete and runnable?

### Before Implementation:
- [ ] Do I understand the exact current architecture?
- [ ] Have I identified all affected files and functions?
- [ ] Do I have working code examples for each change?
- [ ] Are all dependencies clearly identified?
- [ ] Have I tested my understanding by examining the actual codebase?

### Before Completion:
- [ ] Have I tested all acceptance criteria?
- [ ] Does the implementation match the original requirements?
- [ ] Is all documentation updated with actual results?
- [ ] Are there any remaining speculative elements?
- [ ] Do all code examples work as specified?

---

## Documentation Standards

### `requirements.md` Format:
```markdown
# Feature Requirements

## User Story
As a [user type], I want [specific functionality] so that [specific benefit].

## Acceptance Criteria
- [ ] Specific, testable criterion 1
- [ ] Specific, testable criterion 2

## Success Metrics
- Measurable outcome 1
- Measurable outcome 2
```

### `design.md` Format:
```markdown
# Technical Design

## Current Architecture
- File: `path/to/file.py`
- Class: `ClassName` (lines X-Y)
- Method: `method_name()` (line Z)

## Required Changes
- Modify: `specific_method()` in `file.py`
- Add: `new_method()` to `ClassName`
- Update: Signal connections in `other_file.py`

## Integration Points
- Component A connects to Component B via method X
- Data flows from A → B → C
```

### `tasks.md` Format:
```markdown
# Implementation Tasks

## Task 1: [Specific Action]
**File**: `full/path/to/file.py`
**Method**: `method_name(param: type) -> return_type` (verified from codebase)
**Time**: 20 minutes
**Dependencies**: None

**Current Code** (lines X-Y from actual file):
```python
def existing_method(self, param: str) -> bool:
    """Current implementation."""
    return simple_logic_here
```

**New Code** (complete working implementation):
```python
def existing_method(self, param: str) -> bool:
    """Enhanced implementation with new feature."""
    # Parse input
    parsed_data = self._parse_input(param)

    # Apply new logic
    result = self._apply_new_logic(parsed_data)

    # Return result
    return result

def _parse_input(self, param: str) -> dict:
    """Helper method for parsing."""
    return {"parsed": param.strip().lower()}

def _apply_new_logic(self, data: dict) -> bool:
    """Apply the new business logic."""
    return len(data.get("parsed", "")) > 0
```

**Testing**:
1. Call `method_name("test input")` → expect `True`
2. Call `method_name("")` → expect `False`
3. Verify helper methods work independently
```

**CRITICAL**: Every code example must be complete and runnable, not placeholder comments.

---

## Success Criteria

### Protocol Success:
- [ ] No speculative content in any document
- [ ] All file paths and functions verified to exist
- [ ] All tasks are atomic and actionable
- [ ] Complete code examples provided
- [ ] All acceptance criteria testable

### Implementation Success:
- [ ] Feature works as specified
- [ ] No breaking changes
- [ ] All tests pass
- [ ] User has reviewed and approved feature
- [ ] All user feedback addressed
- [ ] Documentation complete and accurate
- [ ] Code follows project patterns

---

**This protocol eliminates speculation and ensures reliable, fact-based feature implementation.**

