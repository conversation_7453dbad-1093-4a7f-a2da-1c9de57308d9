"""
Export Group

Handles all export-related functionality for table data.
Contains export button with menu for different export formats.
"""

from PySide6.QtCore import Signal
from PySide6.QtGui import QAction
from PySide6.QtWidgets import QWidget, QHBoxLayout, QMenu
from fm.gui._shared_components.toolbar import BaseToolbarButton


class ExportButton(BaseToolbarButton):
    """Button for showing export menu with CSV and Excel options."""

    csv_export_requested = Signal()  # Emitted when CSV export is requested
    excel_export_requested = Signal()  # Emitted when Excel export is requested

    def __init__(self, parent=None):
        """Initialize the export button using BaseToolbarButton."""
        super().__init__(
            icon_name="export",
            tooltip="Export Data",
            style_variant="default",
            parent=parent
        )
        self.clicked.connect(self._show_export_menu)
    
    def _show_export_menu(self):
        """Show export options menu."""
        menu = QMenu(self)
        
        # CSV export action
        csv_action = QAction("Export to CSV", self)
        csv_action.triggered.connect(self.csv_export_requested.emit)
        menu.addAction(csv_action)
        
        # Excel export action
        excel_action = QAction("Export to Excel", self)
        excel_action.triggered.connect(self.excel_export_requested.emit)
        menu.addAction(excel_action)
        
        # Show menu relative to button
        menu.exec(self.mapToGlobal(self.rect().bottomRight()))


class ExportGroup(QWidget):
    """Group focused solely on export functionality."""
    
    # Signals for external communication
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the export group."""
        super().__init__(parent)

        # Set CSS class for styling
        self.setObjectName("ExportGroup")

        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)  # Reduced from 5px to 4px for tighter spacing
        
        # Export button
        self.export_button = ExportButton()
        layout.addWidget(self.export_button)
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Forward signals from export button
        self.export_button.csv_export_requested.connect(
            self.csv_export_requested)
        self.export_button.excel_export_requested.connect(
            self.excel_export_requested)
