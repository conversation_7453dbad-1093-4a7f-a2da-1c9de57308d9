#!/usr/bin/env python3
"""
Unit tests for filter persistence and AND/exclude functionality.
"""

import sys
import os

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

def test_table_config():
    """Test that TableConfig has the new filter persistence fields."""
    print("=== Testing TableConfig ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_config_v2 import TableConfig
        
        # Create config instance
        config = TableConfig()
        
        # Test default values
        tests = [
            ("save_filter_state", True),
            ("default_filter_column", "details"),
            ("last_filter_column", None),
            ("last_filter_pattern", None)
        ]
        
        for field, expected in tests:
            if hasattr(config, field):
                actual = getattr(config, field)
                status = "✅ PASS" if actual == expected else "❌ FAIL"
                print(f"{status} {field}: {actual} (expected {expected})")
            else:
                print(f"❌ FAIL {field}: Field not found")
        
        # Test that copy method includes new fields
        config.last_filter_pattern = "test"
        config.last_filter_column = "amount"
        
        copied = config.copy()
        if (copied.last_filter_pattern == "test" and 
            copied.last_filter_column == "amount"):
            print("✅ PASS copy() method includes new fields")
        else:
            print("❌ FAIL copy() method missing new fields")
            
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_filter_logic():
    """Test the enhanced filter logic."""
    print("\n=== Testing Filter Logic ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        # Test data scenarios
        test_cases = [
            # (data_string, pattern, expected_result, description)
            ("Coffee Shop Purchase", "Coffee Shop", True, "AND: both terms present"),
            ("Coffee Shop Purchase", "Coffee Tea", False, "AND: missing term"),
            ("Coffee Shop Purchase", "Coffee -Shop", False, "EXCLUDE: excluded term present"),
            ("Coffee Shop Purchase", "Purchase -Tea", True, "EXCLUDE: excluded term not present"),
            ("Coffee Shop Purchase", "-Tea", True, "EXCLUDE only: excluded term not present"),
            ("Coffee Shop Purchase", "-Coffee", False, "EXCLUDE only: excluded term present"),
            ("Just a dash -", "dash -", True, "Literal dash at end"),
            ("", "", True, "Empty pattern matches empty data"),
            ("Test", "", True, "Empty pattern matches any data"),
        ]
        
        for data, pattern, expected, description in test_cases:
            try:
                result = proxy._check_pattern_match(data, pattern)
                status = "✅ PASS" if result == expected else "❌ FAIL"
                print(f"{status} {description}: '{pattern}' on '{data}' → {result}")
            except Exception as e:
                print(f"❌ ERROR {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_filter_group_methods():
    """Test FilterGroup state management methods."""
    print("\n=== Testing FilterGroup Methods ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.toolbar.groups.filter_group import FilterGroup
        
        # Check if methods exist
        methods_to_check = ['set_filter_state', 'get_filter_state']
        
        for method_name in methods_to_check:
            if hasattr(FilterGroup, method_name):
                print(f"✅ PASS {method_name} method exists")
            else:
                print(f"❌ FAIL {method_name} method not found")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_placeholder_text():
    """Test that FilterInput has updated placeholder text."""
    print("\n=== Testing Placeholder Text ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from fm.gui._shared_components.table_view_v2.components.toolbar.groups.filter_group import FilterInput
        
        # Create minimal QApplication for Qt widgets
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create FilterInput
        filter_input = FilterInput()
        placeholder = filter_input.placeholderText()
        
        expected_keywords = ["AND", "exclude", "space-separated"]
        
        if all(keyword in placeholder for keyword in expected_keywords):
            print(f"✅ PASS Placeholder text updated: '{placeholder}'")
        else:
            print(f"❌ FAIL Placeholder text: '{placeholder}' (missing keywords)")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_table_view_methods():
    """Test CustomTableView_v2 has persistence methods."""
    print("\n=== Testing CustomTableView_v2 Methods ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.fm_table_view import CustomTableView_v2
        
        # Check if methods exist
        methods_to_check = ['_save_filter_state', '_restore_filter_state']
        
        for method_name in methods_to_check:
            if hasattr(CustomTableView_v2, method_name):
                print(f"✅ PASS {method_name} method exists")
            else:
                print(f"❌ FAIL {method_name} method not found")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all unit tests."""
    print("=== Filter Persistence & AND/Exclude Unit Tests ===\n")
    
    tests = [
        ("TableConfig Fields", test_table_config),
        ("Filter Logic", test_filter_logic),
        ("FilterGroup Methods", test_filter_group_methods),
        ("Placeholder Text", test_placeholder_text),
        ("CustomTableView_v2 Methods", test_custom_table_view_methods)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print("=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Filter persistence implementation is complete.")
        print("\n✨ Features implemented:")
        print("  • Filter state persistence in TableConfig")
        print("  • AND logic: space-separated terms (all must match)")
        print("  • EXCLUDE logic: -term excludes rows containing term")
        print("  • Updated UI placeholder text with syntax hints")
        print("  • State management methods in FilterGroup")
        print("  • Persistence methods in CustomTableView_v2")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
