"""
Test Apply Button Detection

This script tests the enhanced apply button detection logic
to verify complex operators trigger the apply button correctly.
"""

import sys
from pathlib import Path

# Add the flatmate source to path for testing
sys.path.insert(0, str(Path(__file__).parent / "flatmate" / "src"))

from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QVBoxLayout, QWidget, QLabel, QPushButton
from PySide6.QtCore import QTimer

try:
    from fm.gui._shared_components.table_view_v2.components.toolbar.integrated_search_field import IntegratedSearchField
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import components: {e}")
    COMPONENTS_AVAILABLE = False


class ApplyButtonDetectionTest(QWidget):
    """Test widget for apply button detection functionality."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Apply Button Detection Test")
        self.setGeometry(100, 100, 700, 400)
        
        layout = QVBoxLayout(self)
        
        # Results display
        self.results_label = QLabel("Testing apply button detection...")
        layout.addWidget(self.results_label)
        
        if COMPONENTS_AVAILABLE:
            self.setup_test()
        else:
            layout.addWidget(QLabel("❌ Components not available - check imports"))
        
        layout.addWidget(self.results_label)
    
    def setup_test(self):
        """Set up the test interface."""
        layout = self.layout()
        
        # Create integrated search field
        self.search_field = IntegratedSearchField()
        
        # Connect signals for testing
        self.search_field.advanced_operators_detected.connect(
            lambda detected: self.log_result(f"🔍 Advanced operators detected: {detected}")
        )
        
        layout.addWidget(self.search_field)
        
        # Test instructions
        instructions = QLabel("""
        Test Instructions - Type these queries to test apply button detection:
        
        SHOULD TRIGGER APPLY BUTTON:
        • (coffee|tea) - parentheses and OR operator
        • coffee -decaf - NOT operator with space
        • -starbucks - leading dash
        • "exact phrase" - quoted strings
        • coffee & tea - AND operator
        • coffee* - wildcard
        • coffee+ - plus operator
        • coffee~ - fuzzy search
        
        SHOULD NOT TRIGGER APPLY BUTTON:
        • simple text - no operators
        • coffee tea - just words
        • 123.45 - numbers
        """)
        instructions.setStyleSheet("color: #888888; font-size: 12px; margin: 10px; background: #333; padding: 10px; border-radius: 4px;")
        layout.addWidget(instructions)
        
        # Test buttons for automated testing
        test_layout = layout
        
        # Complex query tests
        complex_button = QPushButton("Test Complex Queries")
        complex_button.clicked.connect(self.test_complex_queries)
        test_layout.addWidget(complex_button)
        
        # Simple query tests
        simple_button = QPushButton("Test Simple Queries")
        simple_button.clicked.connect(self.test_simple_queries)
        test_layout.addWidget(simple_button)
        
        self.log_result("✅ Apply button detection test ready")
    
    def test_complex_queries(self):
        """Test queries that should trigger apply button."""
        complex_queries = [
            "(coffee|tea)",
            "coffee -decaf", 
            "-starbucks",
            '"exact phrase"',
            "coffee & tea",
            "coffee*",
            "coffee+",
            "coffee~",
            "(-expensive)",
            "coffee | tea"
        ]
        
        self.log_result("🧪 Testing complex queries...")
        
        for i, query in enumerate(complex_queries):
            QTimer.singleShot(i * 1000, lambda q=query: self.test_single_query(q, True))
    
    def test_simple_queries(self):
        """Test queries that should NOT trigger apply button."""
        simple_queries = [
            "simple text",
            "coffee tea",
            "123.45",
            "one word",
            "multiple simple words here",
            ""
        ]
        
        self.log_result("🧪 Testing simple queries...")
        
        for i, query in enumerate(simple_queries):
            QTimer.singleShot(i * 1000, lambda q=query: self.test_single_query(q, False))
    
    def test_single_query(self, query, should_trigger):
        """Test a single query."""
        self.search_field.setText(query)
        expected = "SHOULD" if should_trigger else "should NOT"
        self.log_result(f"📝 Testing: '{query}' - {expected} trigger apply button")
        
        # Clear after a moment
        QTimer.singleShot(500, lambda: self.search_field.clear())
    
    def log_result(self, message):
        """Log test results."""
        current_text = self.results_label.text()
        if "Testing apply button" in current_text:
            self.results_label.setText(message)
        else:
            self.results_label.setText(current_text + "\n" + message)
        print(f"DETECTION_TEST: {message}")


def main():
    """Run the apply button detection test."""
    app = QApplication(sys.argv)
    
    # Set dark theme for testing
    app.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: white;
        }
        QLineEdit {
            background-color: #1E1E1E;
            border: 1px solid #333333;
            border-radius: 4px;
            padding: 6px 8px;
            color: white;
            font-size: 14px;
        }
        QLineEdit:focus {
            border-color: #3B8A45;
        }
        QLabel {
            color: white;
            margin: 5px 0;
        }
        QPushButton {
            background-color: #3B8A45;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            margin: 5px;
        }
        QPushButton:hover {
            background-color: #4BA357;
        }
    """)
    
    test_widget = ApplyButtonDetectionTest()
    test_widget.show()
    
    print("=== Apply Button Detection Test ===")
    print("Testing enhanced pattern detection for complex search operators...")
    print("Watch console output for DEBUG messages showing detection logic")
    print("Use test buttons for automated testing or type manually")
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
