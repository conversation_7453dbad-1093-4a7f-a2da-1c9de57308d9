"""
Simple Apply Button Test

Test the apply button detection by directly calling the methods.
"""

import sys
from pathlib import Path

# Add the flatmate source to path for testing
sys.path.insert(0, str(Path(__file__).parent / "flatmate" / "src"))

from PySide6.QtWidgets import QApplication

try:
    from fm.gui._shared_components.table_view_v2.components.toolbar.integrated_search_field import IntegratedSearchField
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import components: {e}")
    COMPONENTS_AVAILABLE = False


def test_operator_detection():
    """Test the operator detection logic directly."""
    if not COMPONENTS_AVAILABLE:
        print("❌ Components not available")
        return
    
    # Create instance
    search_field = IntegratedSearchField()
    
    # Test cases
    test_cases = [
        # Should trigger apply button (complex)
        ("(coffee|tea)", True),
        ("coffee -decaf", True),
        ("-starbucks", True),
        ('"exact phrase"', True),
        ("coffee & tea", True),
        ("coffee*", True),
        ("coffee+", True),
        ("coffee~", True),
        ("(-expensive)", True),
        ("coffee | tea", True),
        
        # Should NOT trigger apply button (simple)
        ("simple text", False),
        ("coffee tea", False),
        ("123.45", False),
        ("one word", False),
        ("", False),
    ]
    
    print("=== Apply Button Detection Test ===")
    print("Testing operator detection logic...")
    
    for query, should_be_complex in test_cases:
        # Test the quick check method
        is_simple = search_field._is_likely_simple_query(query)
        is_complex_quick = not is_simple
        
        # Test the detailed method
        is_complex_detailed = search_field._has_advanced_operators(query)
        
        # Expected result
        expected = "COMPLEX" if should_be_complex else "SIMPLE"
        quick_result = "COMPLEX" if is_complex_quick else "SIMPLE"
        detailed_result = "COMPLEX" if is_complex_detailed else "SIMPLE"
        
        # Check if results match expectations
        quick_correct = is_complex_quick == should_be_complex
        detailed_correct = is_complex_detailed == should_be_complex
        
        status_quick = "✅" if quick_correct else "❌"
        status_detailed = "✅" if detailed_correct else "❌"
        
        print(f"Query: '{query}'")
        print(f"  Expected: {expected}")
        print(f"  Quick check: {quick_result} {status_quick}")
        print(f"  Detailed check: {detailed_result} {status_detailed}")
        
        if not (quick_correct and detailed_correct):
            print(f"  ⚠️  MISMATCH DETECTED!")
        print()
    
    print("=== Test Complete ===")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    test_operator_detection()
    sys.exit(0)
