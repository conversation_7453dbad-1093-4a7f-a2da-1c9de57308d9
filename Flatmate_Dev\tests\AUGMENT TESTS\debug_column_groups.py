#!/usr/bin/env python3
"""
Debug script to investigate column group issues.
"""

def debug_column_groups():
    """Debug what's in each column group."""
    print("=== Column Groups Debug ===\n")
    
    try:
        from fm.core.data_services.standards.columns import Columns
        
        # Check all available groups
        print("1. Checking all columns and their groups:")
        all_cols = Columns.get_all_columns()
        print(f"   Total columns: {len(all_cols)}")
        
        # Group columns by their groups
        group_map = {}
        for col in all_cols:
            for group in col.groups:
                if group not in group_map:
                    group_map[group] = []
                group_map[group].append(col)
        
        print(f"   Available groups: {list(group_map.keys())}")
        print()
        
        # Check specific groups
        print("2. Core transaction columns:")
        core_cols = Columns.get('core_transaction_cols')
        print(f"   Count: {len(core_cols)}")
        for col in core_cols:
            print(f"   - {col.display_name} ({col.db_name}) order: {col.order}")
        print()
        
        print("3. User editable columns:")
        user_cols = Columns.get('user_editable')
        print(f"   Count: {len(user_cols)}")
        for col in user_cols:
            print(f"   - {col.display_name} ({col.db_name}) order: {col.order}")
        print()
        
        print("4. Display columns (core + user):")
        display_cols = Columns.get_display_columns()
        print(f"   Count: {len(display_cols)}")
        for col in display_cols:
            print(f"   - {col.display_name} ({col.db_name}) order: {col.order}")
        print()
        
        print("5. Ordered display columns for categorize:")
        ordered_cols = Columns.get_ordered_display_columns('categorize')
        print(f"   Count: {len(ordered_cols)}")
        for col in ordered_cols:
            print(f"   - {col.display_name} ({col.db_name}) order: {col.order}")
        print()
        
        print("6. Default visible columns for categorize:")
        default_cols = Columns.get_default_visible_columns('categorize')
        print(f"   Count: {len(default_cols)}")
        print(f"   Columns: {default_cols}")
        print()
        
        # Check if there's a mismatch
        print("7. Checking for issues:")
        if len(core_cols) == 0:
            print("   ❌ No core transaction columns found!")
        if len(display_cols) != len(core_cols) + len(user_cols):
            print(f"   ⚠️  Display columns count mismatch: {len(display_cols)} != {len(core_cols)} + {len(user_cols)}")
        if len(ordered_cols) < 5:
            print(f"   ❌ Too few ordered display columns: {len(ordered_cols)}")
        
        print("\n=== Debug Complete ===")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_column_groups()
