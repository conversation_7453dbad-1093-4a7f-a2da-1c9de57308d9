"""
Migration Guide - From Legacy to Optimized Toolbar

Provides utilities and guidance for migrating from the legacy toolbar to the optimized Flatmate pattern.
"""

from typing import Dict, Any
import warnings

from .table_view_toolbar import TableViewToolbar as LegacyToolbar
from .table_view_toolbar_optimized import TableViewToolbarOptimized


class ToolbarMigrationHelper:
    """Helper class for migrating from legacy to optimized toolbar."""
    
    @staticmethod
    def create_optimized_toolbar(legacy_toolbar: LegacyToolbar) -> TableViewToolbarOptimized:
        """Create an optimized toolbar from legacy configuration."""
        optimized = TableViewToolbarOptimized()
        
        # Migrate configuration
        if hasattr(legacy_toolbar, 'filter_group'):
            # Copy filter configuration
            filter_state = legacy_toolbar.filter_group.get_state() if hasattr(legacy_toolbar.filter_group, 'get_state') else {}
            optimized.filter_group.set_state(filter_state)
        
        if hasattr(legacy_toolbar, 'column_group'):
            # Copy column configuration
            column_state = legacy_toolbar.column_group.get_state() if hasattr(legacy_toolbar.column_group, 'get_state') else {}
            optimized.visible_columns_selector.set_state(column_state)
        
        return optimized
    
    @staticmethod
    def migrate_signals(legacy_toolbar: LegacyToolbar, optimized: TableViewToolbarOptimized):
        """Migrate signal connections from legacy to optimized."""
        # Map legacy signals to optimized signals
        signal_mapping = {
            'filter_applied': optimized.filter_applied,
            'filters_cleared': optimized.filters_cleared,
            'column_visibility_requested': optimized.column_visibility_requested,
            'csv_export_requested': optimized.csv_export_requested,
            'excel_export_requested': optimized.excel_export_requested,
        }
        
        return signal_mapping
    
    @staticmethod
    def validate_migration(legacy_toolbar: LegacyToolbar, optimized: TableViewToolbarOptimized) -> bool:
        """Validate that migration was successful."""
        try:
            # Test basic functionality
            legacy_columns = legacy_toolbar.filter_group.columns if hasattr(legacy_toolbar.filter_group, 'columns') else []
            optimized_columns = optimized.filter_group.columns if hasattr(optimized.filter_group, 'columns') else []
            
            return len(legacy_columns) == len(optimized_columns)
        except Exception as e:
            warnings.warn(f"Migration validation failed: {e}")
            return False


class MigrationWarning:
    """Warning for migration-related issues."""
    pass


def deprecate_legacy_toolbar():
    """Mark the legacy toolbar as deprecated."""
    warnings.warn(
        "TableViewToolbar is deprecated. Use TableViewToolbarOptimized instead.",
        DeprecationWarning,
        stacklevel=2
    )