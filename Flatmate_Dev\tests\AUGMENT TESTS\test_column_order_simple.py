#!/usr/bin/env python3
"""
Simple test script to verify column ordering implementation.
"""

from fm.core.data_services.standards.column_order import StandardColumnsOrder
from fm.core.data_services.standards.column_definition import Column

def test_column_ordering():
    """Test that column ordering works correctly."""
    print("=== Column Order Test ===\n")
    
    # Test 1: StandardColumnsOrder enum
    print("1. StandardColumnsOrder enum values:")
    print(f"   DATE: {StandardColumnsOrder.DATE.value}")
    print(f"   DETAILS: {StandardColumnsOrder.DETAILS.value}")
    print(f"   AMOUNT: {StandardColumnsOrder.AMOUNT.value}")
    print(f"   CATEGORY: {StandardColumnsOrder.CATEGORY.value}")
    print()
    
    # Test 2: Create columns with order values
    print("2. Creating columns with order values:")
    date_col = Column(
        db_name='date', 
        display_name='Date', 
        dtype=str, 
        groups=['core'], 
        width=12, 
        order=StandardColumnsOrder.DATE.value
    )
    
    amount_col = Column(
        db_name='amount', 
        display_name='Amount', 
        dtype=float, 
        groups=['core'], 
        width=12, 
        order=StandardColumnsOrder.AMOUNT.value
    )
    
    category_col = Column(
        db_name='category', 
        display_name='Category', 
        dtype=str, 
        groups=['user'], 
        width=20, 
        order=StandardColumnsOrder.CATEGORY.value
    )
    
    print(f"   Date column order: {date_col.order}")
    print(f"   Amount column order: {amount_col.order}")
    print(f"   Category column order: {category_col.order}")
    print()
    
    # Test 3: Sort columns by order
    print("3. Sorting columns by order:")
    unsorted_cols = [category_col, date_col, amount_col]
    print("   Before sorting:", [col.display_name for col in unsorted_cols])
    
    sorted_cols = sorted(unsorted_cols, key=lambda col: col.order)
    print("   After sorting: ", [col.display_name for col in sorted_cols])
    print()
    
    # Test 4: Test order utility methods
    print("4. Testing StandardColumnsOrder utility methods:")
    print("   get_order_value('DATE'):", StandardColumnsOrder.get_order_value('DATE'))
    print("   get_order_value('UNKNOWN'):", StandardColumnsOrder.get_order_value('UNKNOWN'))
    
    ordered_names = StandardColumnsOrder.get_ordered_column_names()
    print(f"   First 5 ordered column names: {ordered_names[:5]}")
    print()
    
    print("=== Test Complete - Column ordering is working! ===")

if __name__ == "__main__":
    test_column_ordering()
