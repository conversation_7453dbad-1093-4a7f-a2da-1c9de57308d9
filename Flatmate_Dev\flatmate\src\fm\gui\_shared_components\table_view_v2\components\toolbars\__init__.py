"""
Toolbar Components for Table Views

This package provides a complete toolbar system for table widgets with a clear,
scalable architecture for multiple toolbar types across different modules.

Folder Structure:
├── base_toolbar_components/     # Reusable base classes & layout managers
├── table_view_toolbar_v2/      # Default "home" toolbar (recommended)
├── table_view_toolbar_v1/      # Original toolbar (preserved for compatibility)
├── groups/                     # Functional component groups
└── [future: search_builder_toolbar/, custom_toolbars/, etc.]

Main Components:
- TableViewToolbar: The default toolbar (currently V2)
- TableViewToolbarV2: Modern implementation with new layout
- TableViewToolbarV1: Original implementation (fallback)
- Groups: FilterGroup, ColumnGroup, ExportGroup for custom combinations

Usage:
    # Default toolbar (recommended - currently V2)
    from .toolbar import TableViewToolbar

    # Specific versions
    from .toolbar import TableViewToolbarV2, TableViewToolbarV1

    # Base components for custom toolbars
    from .toolbar.base_toolbar_components import ToolbarLayoutManager, BaseToolbarButton

    # Individual groups for custom combinations
    from .toolbar.groups import FilterGroup, ColumnGroup, ExportGroup
"""

# Import the different toolbar versions
from .table_view_toolbar_v1 import TableViewToolbarV1
from .table_view_toolbar_v2 import TableViewToolbarV2
from .groups import FilterGroup, ColumnGroup, ExportGroup

# Default toolbar - currently V2 (the "home" toolbar)
TableViewToolbar = TableViewToolbarV2

# Easy switching between versions
# To use V1: TableViewToolbar = TableViewToolbarV1
# To use V2: TableViewToolbar = TableViewToolbarV2 (current default)

__all__ = [
    # Default toolbar (currently V2)
    'TableViewToolbar',

    # Specific versions
    'TableViewToolbarV1',
    'TableViewToolbarV2',

    # Groups for custom combinations
    'FilterGroup',
    'ColumnGroup',
    'ExportGroup'
]
