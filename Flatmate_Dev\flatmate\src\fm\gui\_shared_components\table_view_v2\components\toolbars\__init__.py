"""
Table View Toolbars

Clear, industry-standard structure for table view toolbars:

├── table_view_toolbar.py          # DEFAULT toolbar (the "home" toolbar)
├── table_view_toolbar_legacy.py   # LEGACY toolbar (original, for rollback)
├── groups/                        # Table-specific toolbar groups
├── utils/                         # Table toolbar utilities
└── integrated_search_field.py     # Existing search field component

Main Components:
- TableViewToolbar: The default toolbar (modern implementation)
- TableViewToolbarLegacy: Original toolbar (for compatibility/rollback)
- Groups: FilterGroup, ColumnGroup, ExportGroup for custom combinations

Usage:
    # Default toolbar (recommended)
    from .toolbars import TableViewToolbar

    # Legacy toolbar (for rollback)
    from .toolbars import TableViewToolbarLegacy

    # Individual groups for custom combinations
    from .toolbars.groups import FilterGroup, ColumnGroup, ExportGroup
"""

# Import the toolbar implementations
from .table_view_toolbar_v2 import TableViewToolbar
from .table_view_toolbar_legacy import TableViewToolbarLegacy
from .components import FilterGroup, ColumnGroup, ExportGroup

__all__ = [
    # Default toolbar
    'TableViewToolbar',

    # Legacy toolbar (for rollback)
    'TableViewToolbarLegacy',

    # Groups for custom combinations
    'FilterGroup',
    'ColumnGroup',
    'ExportGroup'
]
