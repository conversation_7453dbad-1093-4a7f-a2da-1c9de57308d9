"""
Toolbar Manager - Centralized State Management

Manages the state and configuration of the toolbar system using the optimized Flatmate pattern.
"""

from typing import Dict, Any, Optional
from PySide6.QtCore import QObject, Signal


class ToolbarManager(QObject):
    """Centralized state management for the toolbar system."""
    
    state_changed = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._state = {
            'filters': {},
            'columns': {},
            'export_config': {}
        }
    
    def get_state(self) -> Dict[str, Any]:
        """Get the current toolbar state."""
        return self._state.copy()
    
    def set_state(self, state: Dict[str, Any]):
        """Set the toolbar state and emit change signal."""
        self._state.update(state)
        self.state_changed.emit(self._state)
    
    def update_filter_state(self, filter_state: Dict[str, Any]):
        """Update filter state."""
        self._state['filters'].update(filter_state)
        self.state_changed.emit(self._state)
    
    def update_column_state(self, column_state: Dict[str, Any]):
        """Update column state."""
        self._state['columns'].update(column_state)
        self.state_changed.emit(self._state)
    
    def update_export_config(self, export_config: Dict[str, Any]):
        """Update export configuration."""
        self._state['export_config'].update(export_config)
        self.state_changed.emit(self._state)
    
    def get_filter_state(self) -> Dict[str, Any]:
        """Get current filter state."""
        return self._state.get('filters', {})
    
    def get_column_state(self) -> Dict[str, Any]:
        """Get current column state."""
        return self._state.get('columns', {})
    
    def get_export_config(self) -> Dict[str, Any]:
        """Get current export configuration."""
        return self._state.get('export_config', {})