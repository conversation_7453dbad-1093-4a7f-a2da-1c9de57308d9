# Flatmate Testing Protocols

## Environment Setup

### Virtual Environment
- **Location**: `flatmate/.venv_fm313`
- **Activation**: Must be run from `/flatmate` directory
- **Python Version**: 3.13

### Running Tests

#### Method 1: Activate Environment First
```bash
cd flatmate
.venv_fm313\Scripts\activate  # Windows
source .venv_fm313/bin/activate  # Linux/Mac

# Then run tests
python src/fm/gui/_shared_components/table_view_v2/components/toolbar/test_toolbar_v3.py
```

#### Method 2: Direct Execution
```bash
cd flatmate
.venv_fm313\Scripts\python.exe src/fm/gui/_shared_components/table_view_v2/components/toolbar/test_toolbar_v3.py
```

## GUI Component Testing

### Toolbar Testing
1. **Visual Layout Check**
   - Verify component order: `[Eye] [Search] "in:" [Dropdown] [Export]`
   - Check spacing and alignment
   - Confirm responsive behavior

2. **Functionality Testing**
   - Type in search box → should see filter signals
   - Change dropdown selection → should trigger re-filter
   - Click buttons → should emit appropriate signals
   - Clear search → should emit clear signal

3. **Data Filtering Testing**
   - Verify internal columns are hidden from dropdown
   - Check column name formatting (snake_case → Title Case)
   - Confirm "All Visible Columns" option appears when multiple columns

### Test Script Pattern
```python
# Standard test script structure
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parents[N]  # Adjust N as needed
sys.path.insert(0, str(project_root))

# Import and test components
from your_component import YourComponent

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # Setup test window
        # Connect signals for debugging
        # Apply test styling
    
    def _connect_test_signals(self):
        # Connect all signals to debug handlers
        pass

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())
```

## Module Testing

### Table View Components
- **Location**: `src/fm/gui/_shared_components/table_view_v2/`
- **Test Pattern**: Create isolated test windows
- **Dependencies**: Ensure all imports resolve correctly

### Search and Filtering
- **Performance Testing**: Test with large datasets
- **Edge Cases**: Empty searches, special characters, advanced operators
- **Live Filtering**: Verify debouncing works correctly

## Integration Testing

### Module Integration
1. **Import Testing**: Verify all imports work in isolation
2. **Signal Flow**: Test signal connections end-to-end
3. **State Management**: Test persistence and restoration
4. **Error Handling**: Test graceful failure modes

### Full Application Testing
1. **Navigation**: Test module switching
2. **Data Flow**: Test data loading and filtering
3. **Performance**: Monitor memory usage and response times
4. **User Workflows**: Test complete user scenarios

## Debugging Protocols

### Common Issues
1. **Import Errors**: Check Python path and virtual environment
2. **QSS Not Applied**: Verify object names and CSS selectors
3. **Signals Not Connected**: Check signal/slot connections
4. **Layout Issues**: Verify parent/child relationships

### Debug Tools
```python
# Add debug prints for signal testing
def debug_signal_handler(self, *args):
    print(f"DEBUG: Signal received with args: {args}")

# QSS debugging
widget.setStyleSheet("background-color: red;")  # Obvious test color

# Layout debugging
print(f"Widget size: {widget.size()}")
print(f"Layout count: {layout.count()}")
```

## Performance Testing

### Memory Usage
- Monitor with Task Manager or `psutil`
- Check for memory leaks during extended use
- Verify cleanup on component destruction

### Response Times
- Search filtering should be < 100ms for typical datasets
- UI updates should be immediate
- Large dataset handling should remain responsive

## Test Documentation

### Test Results Format
```markdown
## Test: Toolbar V3 Layout
**Date**: 2025-07-19
**Tester**: [Name]
**Environment**: Windows 11, Python 3.13, .venv_fm313

### Results
- [x] Layout displays correctly
- [x] Internal columns filtered
- [x] Search signals work
- [ ] Export button styling needs adjustment

### Issues Found
1. Export button icon not loading
2. Search debouncing too aggressive

### Next Steps
- Fix icon loading path
- Reduce debounce delay to 200ms
```

## Automated Testing

### Unit Tests
- Location: `tests/` directory (when created)
- Framework: pytest recommended
- Coverage: Aim for >80% on critical components

### Integration Tests
- Test complete workflows
- Mock external dependencies
- Verify error handling

## Best Practices

### Before Testing
1. **Clean Environment**: Fresh virtual environment activation
2. **Clear Logs**: Clear any existing log files
3. **Baseline Check**: Ensure app runs normally first

### During Testing
1. **Document Everything**: Screenshots, error messages, steps
2. **Test Incrementally**: One feature at a time
3. **Check Console**: Monitor debug output

### After Testing
1. **Document Results**: Update test documentation
2. **Report Issues**: Create clear issue descriptions
3. **Clean Up**: Deactivate environment, close processes

## Quick Reference

### Common Commands
```bash
# Activate environment
cd flatmate && .venv_fm313\Scripts\activate

# Run specific test
python src/path/to/test_file.py

# Check Python path
python -c "import sys; print('\n'.join(sys.path))"

# Check installed packages
pip list

# Deactivate environment
deactivate
```

### Environment Variables
- Ensure `PYTHONPATH` includes project root if needed
- Check `QT_QPA_PLATFORM` for headless testing

This protocol ensures consistent, reliable testing across all Flatmate components.
