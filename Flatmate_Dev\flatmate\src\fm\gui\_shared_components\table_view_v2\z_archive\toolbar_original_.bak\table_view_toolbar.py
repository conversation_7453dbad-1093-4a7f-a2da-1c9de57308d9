"""
Table View Toolbar

Main toolbar component that combines filter, column, and export groups
into a complete toolbar solution for table widgets.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFrame, QHBoxLayout

from .groups import FilterGroup, ColumnGroup, ExportGroup


class TableViewToolbar(QFrame):
    """Complete toolbar combining filter, column, and export groups."""
    
    # Signals for external communication
    filter_applied = Signal(object, str)  # column (int or str), pattern
    filters_cleared = Signal()
    column_visibility_requested = Signal()
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the table view toolbar."""
        super().__init__(parent)

        # Set object name for styling
        self.setObjectName("TableViewToolbar")

        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components with responsive layout."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)  # Proper padding for visual balance
        layout.setSpacing(12)  # Adequate spacing between groups
        
        # Filter group (left side)
        self.filter_group = FilterGroup()
        layout.addWidget(self.filter_group)
        
        # Spacer to push action groups to the right
        layout.addStretch()

        # Column group (right side)
        self.column_group = ColumnGroup()
        layout.addWidget(self.column_group)

        # Export group (right side)
        self.export_group = ExportGroup()
        layout.addWidget(self.export_group)
    
    def _connect_signals(self):
        """Connect group signals to toolbar signals."""
        # Forward filter group signals
        self.filter_group.filter_applied.connect(self.filter_applied)
        self.filter_group.filters_cleared.connect(self.filters_cleared)
        
        # Forward column group signals
        self.column_group.column_visibility_requested.connect(
            self.column_visibility_requested)

        # Forward export group signals
        self.export_group.csv_export_requested.connect(
            self.csv_export_requested)
        self.export_group.excel_export_requested.connect(
            self.excel_export_requested)
    
    def set_columns(self, columns, column_names=None):
        """Set available columns for the toolbar.

        Args:
            columns: List of column identifiers
            column_names: Optional dict mapping column IDs to display names
        """
        self.filter_group.set_columns(columns, column_names)
