"""
Debug Toolbar - Minimal Test

Let's start with the absolute basics and build up to find where the issue is.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parents[7]
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import Q<PERSON>pplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton, QLineEdit, QComboBox
from PySide6.QtCore import Qt


class MinimalToolbar(QFrame):
    """Absolutely minimal toolbar to test basic functionality."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("MinimalToolbar")
        
        # Create layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(8)
        
        # Add simple components
        self.button1 = QPushButton("Eye")
        self.button1.setFixedSize(60, 32)
        layout.addWidget(self.button1)
        
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search...")
        layout.addWidget(self.search_box, 1)  # stretch
        
        self.label = QLabel("in:")
        layout.addWidget(self.label)
        
        self.dropdown = QComboBox()
        self.dropdown.addItems(["Details", "Amount", "Date"])
        layout.addWidget(self.dropdown)
        
        self.button2 = QPushButton("Export")
        self.button2.setFixedSize(60, 32)
        layout.addWidget(self.button2)
        
        # Apply basic styling
        self.setStyleSheet("""
            QFrame#MinimalToolbar {
                background-color: #1E1E1E;
                border: 2px solid #3B8A45;
                border-radius: 4px;
                padding: 4px;
            }
            
            QPushButton {
                background-color: #333333;
                color: white;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 4px;
            }
            
            QPushButton:hover {
                background-color: #3B8A45;
            }
            
            QLineEdit {
                background-color: #2D2D2D;
                color: white;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 4px;
            }
            
            QComboBox {
                background-color: #2D2D2D;
                color: white;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 4px;
            }
            
            QLabel {
                color: #CCCCCC;
            }
        """)
        
        # Connect test signals
        self.button1.clicked.connect(lambda: print("Eye button clicked"))
        self.button2.clicked.connect(lambda: print("Export button clicked"))
        self.search_box.textChanged.connect(lambda text: print(f"Search: {text}"))
        self.dropdown.currentTextChanged.connect(lambda text: print(f"Column: {text}"))


class DebugTestWindow(QMainWindow):
    """Simple test window for debugging."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Debug Toolbar Test")
        self.setGeometry(100, 100, 800, 150)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Add title
        title = QLabel("Debug Toolbar Test - Should show: [Eye] [Search] 'in:' [Dropdown] [Export]")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px; color: white;")
        layout.addWidget(title)
        
        # Create and add the minimal toolbar
        self.toolbar = MinimalToolbar()
        layout.addWidget(self.toolbar)
        
        # Add status
        self.status = QLabel("Ready - Try clicking buttons and typing")
        self.status.setStyleSheet("margin: 10px; color: #CCCCCC;")
        layout.addWidget(self.status)
        
        # Add stretch
        layout.addStretch()
        
        # Apply dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1E1E1E;
            }
            QWidget {
                background-color: #1E1E1E;
            }
        """)


def main():
    """Run the debug test."""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = DebugTestWindow()
    window.show()
    
    print("Debug Toolbar Test Started")
    print("=" * 50)
    print("This should show a simple toolbar with:")
    print("1. Eye button (clickable)")
    print("2. Search box (typeable)")
    print("3. 'in:' label")
    print("4. Dropdown with options")
    print("5. Export button (clickable)")
    print("=" * 50)
    print("If this works, the issue is in the base classes.")
    print("If this doesn't work, the issue is more fundamental.")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
