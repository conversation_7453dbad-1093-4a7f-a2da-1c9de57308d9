# Core Development Protocols

**Purpose**: Essential protocols that apply to all development work  
**Usage**: These protocols form the foundation of the development workflow  

---

## Protocol Links

### **Unified Work Session Protocol**
**Location**: `.augment/rules/unified-work-session-protocol.md`  
**Purpose**: Master protocol for all work sessions (FEATURE, REF<PERSON>TOR, TRO<PERSON><PERSON>ESHO<PERSON>, MAINTENANCE)  
**When to Use**: Start of every work session  
**Key Features**:
- Session classification and setup
- Real-time documentation requirements
- Session completion checklist
- Integration with all other protocols

### **Feature Development Protocol**
**Location**: `.augment/rules/feature-protocol_v1.1.md`  
**Purpose**: Complete feature development process from requirements to delivery  
**When to Use**: When building new functionality  
**Key Features**:
- Requirements gathering (no speculation)
- Architecture analysis with codebase retrieval
- Atomic task breakdown with complete code examples
- User review and feedback integration

### **Documentation Update Protocol**
**Location**: `.augment/rules/update-docs.md`  
**Purpose**: Post-session documentation workflow ensuring no work is lost  
**When to Use**: End of every work session (MANDATORY)  
**Key Features**:
- Session changelog creation
- Architecture documentation updates
- Technical debt recording
- Lesson learned integration

### **Troubleshooting Protocol**
**Location**: `.windsurf/workflows/trouble-shoot.md`  
**Purpose**: Systematic problem-solving and bug resolution  
**When to Use**: When fixing bugs or resolving issues  
**Key Features**:
- Structured problem clarification
- Evidence gathering and analysis
- Hypothesis testing with minimal changes
- Solution documentation and verification

---

## Protocol Integration Flow

```
Start Work Session
    ↓
Unified Work Session Protocol (Session Setup)
    ↓
Choose Specific Protocol:
    ├─ New Feature → Feature Development Protocol
    ├─ Code Improvement → (Use session protocol + component protocol if GUI)
    ├─ Bug Fix → Troubleshooting Protocol
    └─ Maintenance → (Use session protocol)
    ↓
Work Execution (with real-time documentation)
    ↓
Session Completion
    ↓
Documentation Update Protocol (MANDATORY)
    ↓
Self-Improvement System (Lessons Learned)
```

---

## Quick Reference

### **Every Session Must Have**:
- [ ] **Session classification** (FEATURE/REFACTOR/TROUBLESHOOT/MAINTENANCE)
- [ ] **SESSION_LOG.md** with real-time documentation
- [ ] **Lessons learned section** completed
- [ ] **CHANGELOG.md** created following documentation protocol
- [ ] **Technical debt** recorded if identified
- [ ] **Next steps** clearly defined

### **Session Setup** (2-3 minutes):
1. Create session folder: `flatmate/DOCS/_FEATURES/<session_name>/`
2. Copy session template: `SESSION_LOG_TEMPLATE.md`
3. Fill in header information and objectives
4. Begin real-time logging

### **Session Completion** (10-15 minutes):
1. Complete lessons learned section
2. Create/update CHANGELOG.md
3. Update architecture docs if needed
4. Record technical debt
5. Archive evidence files
6. Apply immediate improvements
7. Mark session status

---

## Protocol Maintenance

### **These protocols are living documents** that evolve based on:
- **Lessons learned** from actual usage
- **Weekly reviews** identifying patterns
- **Monthly evolution** addressing systemic issues
- **User feedback** on protocol effectiveness

### **To suggest protocol improvements**:
1. **Document issues** in session logs
2. **Note in lessons learned** section
3. **Flag for weekly review**
4. **Propose specific changes** with rationale

---

**These core protocols ensure consistent, high-quality development work with comprehensive documentation and continuous improvement.**
