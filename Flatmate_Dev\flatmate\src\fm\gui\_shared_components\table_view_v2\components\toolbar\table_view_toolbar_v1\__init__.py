"""
Table View Toolbar V1 - Original Implementation

This is the original table view toolbar implementation, preserved for:
- Backward compatibility
- Reference implementation
- Fallback option if V2 has issues

The V1 toolbar uses the group-based architecture with:
- FilterGroup: Search and filtering controls
- ColumnGroup: Column visibility management  
- ExportGroup: Export functionality

Usage:
    from .table_view_toolbar_v1 import TableViewToolbarV1
    
    toolbar = TableViewToolbarV1()
    toolbar.set_columns(columns, column_names)
    toolbar.filter_applied.connect(your_handler)
"""

from .table_view_toolbar import TableViewToolbar as TableViewToolbarV1

__all__ = [
    'TableViewToolbarV1'
]
