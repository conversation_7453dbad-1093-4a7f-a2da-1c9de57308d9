"""
Test caching performance optimization.
"""

from search_query_parser import SearchQueryParser
import time

def test_caching_performance():
    """Test the performance improvement from query caching."""
    print("Testing Query Caching Performance")
    print("=" * 40)
    
    parser = SearchQueryParser()
    
    test_queries = [
        "coffee OR tea",
        "restaurant -lunch",
        "(coffee|tea) -decaf",
        '"coffee shop"',
        "bank AND transfer"
    ]
    
    print("\nTesting cache performance for repeated queries...")
    
    for query in test_queries:
        # Clear any existing cache by creating new parser
        parser = SearchQueryParser()
        
        # First parse (cache miss)
        start = time.perf_counter()
        for _ in range(50):
            try:
                parser.parse(query)
            except:
                pass
        end = time.perf_counter()
        first_time = (end - start) * 1000 / 50
        
        # Second parse (cache hit)
        start = time.perf_counter()
        for _ in range(50):
            try:
                parser.parse(query)
            except:
                pass
        end = time.perf_counter()
        cached_time = (end - start) * 1000 / 50
        
        if first_time > 0:
            improvement = ((first_time - cached_time) / first_time * 100)
        else:
            improvement = 0
        
        print(f"Query: '{query}'")
        print(f"  Cache miss: {first_time:.3f}ms")
        print(f"  Cache hit:  {cached_time:.3f}ms")
        print(f"  Improvement: {improvement:.1f}%")
        print()

if __name__ == "__main__":
    test_caching_performance()
