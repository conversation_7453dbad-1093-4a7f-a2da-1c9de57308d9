#!/usr/bin/env python3
"""
Test script for basic grouping functionality in filter logic.
"""

import sys
import os

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

def test_grouping_parsing():
    """Test the basic grouping pattern parsing."""
    print("=== Testing Grouping Pattern Parsing ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        test_cases = [
            # (pattern, expected_type, description)
            ("(coffee|tea)", "grouped_expression", "Simple grouped OR"),
            ("(coffee|tea) -decaf", "grouped_expression", "Grouped OR with exclude"),
            ("(coffee|tea) hot", "grouped_expression", "Grouped OR with AND term"),
            ("coffee (tea|hot)", "grouped_expression", "AND term with grouped OR"),
            ("(coffee)", "grouped_expression", "Single term in parentheses"),
            ("coffee|tea", "or_expression", "No parentheses - fallback to OR"),
            ("coffee tea", "and_exclude", "No parentheses - fallback to AND"),
        ]
        
        for pattern, expected_type, description in test_cases:
            try:
                result = proxy._parse_filter_pattern_v2(pattern)
                actual_type = result["type"]
                status = "✅ PASS" if actual_type == expected_type else "❌ FAIL"
                print(f"{status} {description}: '{pattern}' → {actual_type} (expected {expected_type})")
                
                if actual_type == "grouped_expression":
                    print(f"    Grouped part: {result.get('grouped_part', {})}")
                    print(f"    Remaining part: {result.get('remaining_part', {})}")
                    
            except Exception as e:
                print(f"❌ ERROR {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_grouping_matching():
    """Test the grouping matching logic."""
    print("\n=== Testing Grouping Matching Logic ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        test_cases = [
            # (data, pattern, expected_result, description)
            ("Coffee Shop Purchase", "(coffee|tea)", True, "Grouped OR: coffee matches"),
            ("Tea House Visit", "(coffee|tea)", True, "Grouped OR: tea matches"),
            ("Restaurant Bill", "(coffee|tea)", False, "Grouped OR: neither matches"),
            ("Coffee Shop Purchase", "(coffee|tea) -shop", False, "Grouped OR with exclude: exclude matches"),
            ("Tea House Visit", "(coffee|tea) -shop", True, "Grouped OR with exclude: exclude doesn't match"),
            ("Hot Coffee Shop", "(coffee|tea) hot", True, "Grouped OR with AND: both match"),
            ("Cold Tea House", "(coffee|tea) hot", False, "Grouped OR with AND: OR matches but AND doesn't"),
            ("Hot Restaurant", "(coffee|tea) hot", False, "Grouped OR with AND: AND matches but OR doesn't"),
            ("Starbucks Coffee Shop", "coffee (shop|store)", True, "AND with grouped OR: both match"),
            ("Starbucks Coffee Market", "coffee (shop|store)", False, "AND with grouped OR: AND matches but OR doesn't"),
            ("Tea Shop", "coffee (shop|store)", False, "AND with grouped OR: OR matches but AND doesn't"),
        ]
        
        for data, pattern, expected, description in test_cases:
            try:
                result = proxy._check_pattern_match(data, pattern)
                status = "✅ PASS" if result == expected else "❌ FAIL"
                print(f"{status} {description}")
                print(f"    Data: '{data}', Pattern: '{pattern}' → {result} (expected {expected})")
                
            except Exception as e:
                print(f"❌ ERROR {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complex_expressions():
    """Test complex expressions combining multiple features."""
    print("\n=== Testing Complex Expressions ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        test_cases = [
            # (data, pattern, expected_result, description)
            ("Hot Coffee Shop", "(coffee|tea) (hot|cold)", True, "Two grouped ORs: both match"),
            ("Cold Tea House", "(coffee|tea) (hot|cold)", True, "Two grouped ORs: both match different terms"),
            ("Warm Coffee Shop", "(coffee|tea) (hot|cold)", False, "Two grouped ORs: first matches, second doesn't"),
            ("Hot Restaurant", "(coffee|tea) (hot|cold)", False, "Two grouped ORs: second matches, first doesn't"),
            ("Decaf Coffee Shop", "(coffee|tea) -decaf", False, "Grouped OR with exclude: OR matches but exclude applies"),
            ("Hot Tea House", "(coffee|tea) -decaf", True, "Grouped OR with exclude: OR matches and exclude doesn't apply"),
        ]
        
        for data, pattern, expected, description in test_cases:
            try:
                result = proxy._check_pattern_match(data, pattern)
                status = "✅ PASS" if result == expected else "❌ FAIL"
                print(f"{status} {description}")
                print(f"    Data: '{data}', Pattern: '{pattern}' → {result} (expected {expected})")
                
            except Exception as e:
                print(f"❌ ERROR {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all grouping functionality tests."""
    print("=== Grouping Functionality Tests ===\n")
    
    tests = [
        ("Grouping Pattern Parsing", test_grouping_parsing),
        ("Grouping Matching Logic", test_grouping_matching),
        ("Complex Expressions", test_complex_expressions)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print("=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Basic grouping functionality is working correctly.")
        print("\n✨ New features available:")
        print("  • Basic grouping: (coffee|tea) hot")
        print("  • Grouped OR with exclude: (coffee|tea) -decaf")
        print("  • Mixed grouping: coffee (shop|store)")
        print("  • All previous functionality still works")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
