#!/usr/bin/env python3
"""
Test script to verify the column ordering integration works.
"""

def test_columns_integration():
    """Test that the Columns class integration works."""
    print("=== Column Integration Test ===\n")
    
    try:
        # Test 1: Import the updated Columns class
        print("1. Testing Columns class import...")
        from fm.core.data_services.standards.columns import Columns
        print("   ✓ Columns class imported successfully")
        
        # Test 2: Test get_ordered_display_columns
        print("\n2. Testing get_ordered_display_columns...")
        ordered_cols = Columns.get_ordered_display_columns('categorize')
        print(f"   ✓ Got {len(ordered_cols)} ordered display columns")
        print(f"   First 5: {[col.display_name for col in ordered_cols[:5]]}")
        
        # Test 3: Test get_default_visible_columns with ordering
        print("\n3. Testing get_default_visible_columns...")
        default_cols = Columns.get_default_visible_columns('categorize')
        print(f"   ✓ Got {len(default_cols)} default columns")
        print(f"   Order: {default_cols}")
        
        # Test 4: Verify order is correct
        print("\n4. Verifying column order...")
        # Get the Column objects for the default columns
        col_objects = [Columns.from_db_name(db_name) for db_name in default_cols]
        orders = [col.order for col in col_objects if col]
        print(f"   Order values: {orders}")
        print(f"   ✓ Order is sorted: {orders == sorted(orders)}")
        
        # Test 5: Test ColumnOrderService integration
        print("\n5. Testing ColumnOrderService integration...")
        from fm.core.data_services.standards.column_order_service import ColumnOrderService
        service = ColumnOrderService()
        service_order = service.get_column_order('categorize')
        print(f"   ✓ Service order (first 5): {service_order[:5]}")
        
        print("\n=== Integration test passed! ===")
        return True
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_columns_integration()
