"""
Toolbar Factory - Component Creation and Management

Factory pattern implementation for creating toolbar components using the optimized Flatmate pattern.
"""

from typing import Dict, Any, Optional, Type
from PySide6.QtWidgets import QWidget

from .groups import FilterGroup, ColumnGroup, ExportGroup


class ToolbarFactory:
    """Factory for creating toolbar components with consistent configuration."""
    
    def __init__(self):
        self._group_registry = {}
        self._register_default_groups()
    
    def _register_default_groups(self):
        """Register default toolbar groups."""
        self.register_group('filter', FilterGroup)
        self.register_group('column', ColumnGroup)
        self.register_group('export', ExportGroup)
    
    def register_group(self, group_type: str, group_class: Type[QWidget]):
        """Register a new toolbar group type."""
        self._group_registry[group_type] = group_class
    
    def create_filter_group(self, parent=None) -> FilterGroup:
        """Create a filter group instance."""
        return FilterGroup(parent)
    
    def create_column_group(self, parent=None) -> ColumnGroup:
        """Create a column group instance."""
        return ColumnGroup(parent)
    
    def create_export_group(self, parent=None) -> ExportGroup:
        """Create an export group instance."""
        return ExportGroup(parent)
    
    def create_group(self, group_type: str, parent=None) -> QWidget:
        """Create a toolbar group by type."""
        if group_type not in self._group_registry:
            raise ValueError(f"Unknown group type: {group_type}")
        
        group_class = self._group_registry[group_type]
        return group_class(parent)
    
    def get_registered_groups(self) -> Dict[str, Type[QWidget]]:
        """Get all registered group types."""
        return self._group_registry.copy()