"""
Toolbar Layout Manager

Centralized layout management for all toolbars in the application.
Provides consistent spacing, sizing, and arrangement patterns.
"""

from typing import Dict, List, Optional, Union
from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QToolBar, QFrame,
    QSpacerItem, QSizePolicy, QLabel
)


class ToolbarLayoutManager:
    """Manages layout and positioning for toolbar components."""
    
    # Standard layout configurations
    LAYOUT_CONFIGS = {
        'table_view': {
            'margins': (8, 4, 8, 4),
            'spacing': 8,
            'button_spacing': 4,
            'group_spacing': 12
        },
        'compact': {
            'margins': (4, 2, 4, 2),
            'spacing': 4,
            'button_spacing': 2,
            'group_spacing': 8
        },
        'spacious': {
            'margins': (12, 6, 12, 6),
            'spacing': 12,
            'button_spacing': 6,
            'group_spacing': 16
        }
    }
    
    def __init__(self, config_name: str = 'table_view'):
        """Initialize layout manager with configuration.
        
        Args:
            config_name: Layout configuration to use
        """
        self.config = self.LAYOUT_CONFIGS.get(config_name, self.LAYOUT_CONFIGS['table_view'])
        self._layout = None
        self._widgets = {}
        self._groups = {}
    
    def setup_horizontal_layout(self, parent: QWidget) -> QHBoxLayout:
        """Set up horizontal toolbar layout.
        
        Args:
            parent: Parent widget to apply layout to
            
        Returns:
            Configured QHBoxLayout
        """
        layout = QHBoxLayout(parent)
        layout.setContentsMargins(*self.config['margins'])
        layout.setSpacing(self.config['spacing'])
        
        self._layout = layout
        self._widgets = {}
        self._groups = {}
        
        return layout
    
    def setup_qtoolbar_layout(self, parent: QToolBar) -> QToolBar:
        """Configure QToolBar with standard settings.
        
        Args:
            parent: QToolBar to configure
            
        Returns:
            Configured QToolBar
        """
        parent.setMovable(False)
        parent.setFloatable(False)
        parent.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonIconOnly)
        parent.setIconSize(parent.iconSize())  # Keep default or set specific size
        
        # Apply QSS styling
        parent.setObjectName("StandardToolbar")
        
        return parent
    
    def add_left_group(self, widgets: List[QWidget], group_name: str = "left") -> None:
        """Add widgets to left side of toolbar.
        
        Args:
            widgets: List of widgets to add
            group_name: Name for this group
        """
        if not self._layout:
            raise RuntimeError("Layout not initialized. Call setup_horizontal_layout first.")
        
        group_widget = self._create_widget_group(widgets, self.config['button_spacing'])
        self._layout.addWidget(group_widget)
        self._groups[group_name] = group_widget
    
    def add_center_group(self, widgets: List[QWidget], group_name: str = "center", 
                        stretch: int = 1) -> None:
        """Add widgets to center of toolbar with stretch.
        
        Args:
            widgets: List of widgets to add
            group_name: Name for this group
            stretch: Stretch factor for expansion
        """
        if not self._layout:
            raise RuntimeError("Layout not initialized. Call setup_horizontal_layout first.")
        
        group_widget = self._create_widget_group(widgets, self.config['button_spacing'])
        self._layout.addWidget(group_widget, stretch)
        self._groups[group_name] = group_widget
    
    def add_right_group(self, widgets: List[QWidget], group_name: str = "right") -> None:
        """Add widgets to right side of toolbar.
        
        Args:
            widgets: List of widgets to add
            group_name: Name for this group
        """
        if not self._layout:
            raise RuntimeError("Layout not initialized. Call setup_horizontal_layout first.")
        
        # Add stretch before right group to push it to the right
        if not any(isinstance(self._layout.itemAt(i), QSpacerItem) 
                  for i in range(self._layout.count())):
            self._layout.addStretch()
        
        group_widget = self._create_widget_group(widgets, self.config['button_spacing'])
        self._layout.addWidget(group_widget)
        self._groups[group_name] = group_widget
    
    def add_separator(self) -> None:
        """Add visual separator between groups."""
        if not self._layout:
            raise RuntimeError("Layout not initialized. Call setup_horizontal_layout first.")
        
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setObjectName("ToolbarSeparator")
        self._layout.addWidget(separator)
    
    def add_stretch(self) -> None:
        """Add stretch space to push subsequent widgets to the right."""
        if not self._layout:
            raise RuntimeError("Layout not initialized. Call setup_horizontal_layout first.")
        
        self._layout.addStretch()
    
    def _create_widget_group(self, widgets: List[QWidget], spacing: int) -> QWidget:
        """Create a widget group with consistent spacing.
        
        Args:
            widgets: Widgets to group
            spacing: Spacing between widgets
            
        Returns:
            QWidget containing the grouped widgets
        """
        group = QWidget()
        layout = QHBoxLayout(group)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(spacing)
        
        for widget in widgets:
            layout.addWidget(widget)
        
        return group
    
    def get_group(self, group_name: str) -> Optional[QWidget]:
        """Get a widget group by name.
        
        Args:
            group_name: Name of the group
            
        Returns:
            QWidget group or None if not found
        """
        return self._groups.get(group_name)
    
    def update_spacing(self, spacing: int) -> None:
        """Update spacing for the main layout.
        
        Args:
            spacing: New spacing value
        """
        if self._layout:
            self._layout.setSpacing(spacing)
    
    def update_margins(self, left: int, top: int, right: int, bottom: int) -> None:
        """Update margins for the main layout.
        
        Args:
            left, top, right, bottom: Margin values
        """
        if self._layout:
            self._layout.setContentsMargins(left, top, right, bottom)


class TableViewToolbarLayout:
    """Specialized layout for table view toolbars.
    
    Implements the specific layout pattern:
    [Column Button] [Search Group] [Export Button]
    Where Search Group = [Search Input] "in:" [Column Dropdown]
    """
    
    def __init__(self):
        self.layout_manager = ToolbarLayoutManager('table_view')
    
    def setup_layout(self, parent: QWidget) -> QHBoxLayout:
        """Set up the table view toolbar layout.
        
        Args:
            parent: Parent widget
            
        Returns:
            Configured layout
        """
        return self.layout_manager.setup_horizontal_layout(parent)
    
    def add_column_button(self, button: QWidget) -> None:
        """Add column visibility button to left side."""
        self.layout_manager.add_left_group([button], "column_visibility")
    
    def add_search_group(self, search_input: QWidget, column_dropdown: QWidget, 
                        in_label: Optional[QWidget] = None) -> None:
        """Add search group to center with expansion.
        
        Args:
            search_input: Search text input widget
            column_dropdown: Column selection dropdown
            in_label: Optional "in:" label widget
        """
        widgets = [search_input]
        if in_label:
            widgets.append(in_label)
        widgets.append(column_dropdown)
        
        self.layout_manager.add_center_group(widgets, "search", stretch=1)
    
    def add_export_button(self, button: QWidget) -> None:
        """Add export button to right side."""
        self.layout_manager.add_right_group([button], "export")
    
    def add_separator_before_export(self) -> None:
        """Add separator before export button."""
        self.layout_manager.add_separator()


# Convenience function for quick toolbar setup
def create_standard_toolbar_layout(parent: QWidget, 
                                 config: str = 'table_view') -> ToolbarLayoutManager:
    """Create and configure a standard toolbar layout.
    
    Args:
        parent: Parent widget
        config: Layout configuration name
        
    Returns:
        Configured ToolbarLayoutManager
    """
    manager = ToolbarLayoutManager(config)
    manager.setup_horizontal_layout(parent)
    return manager
