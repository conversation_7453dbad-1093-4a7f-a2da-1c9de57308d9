"""
Integrated Search Field Component

Advanced search field with embedded apply button inside the text field
and external clear button, designed for maximum text input space.

This component implements the refined search container layout:
- Apply button embedded inside text field (trailing position)
- Clear button positioned outside text field (right aligned)
- Search icon as decorative element (leading position)
- Maximum space prioritized for text input
"""

from PySide6.QtCore import Signal, QTimer, QSize
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLineEdit
from PySide6.QtGui import QAction
from fm.gui._shared_components.toolbar import IntegratedTextButton, create_integrated_clear_button


class IntegratedSearchField(QWidget):
    """Advanced search field with embedded apply button and external clear button.
    
    Layout Structure:
    [Search Icon] [Text Input with embedded Apply Button] [External Clear Button]
    
    Features:
    - Apply button embedded inside text field using QLineEdit.addAction()
    - Clear button positioned outside for easy access
    - Search icon as decorative leading element
    - Dynamic apply button (shows only when needed)
    - Maximum text input space utilization
    """
    
    # Signals for external communication
    filter_changed = Signal(str)  # Emits filter text as user types (live filtering)
    filter_requested = Signal(str)  # Emits filter text when apply button clicked
    advanced_operators_detected = Signal(bool)  # Emits True when advanced operators detected
    
    def __init__(self, parent=None):
        """Initialize the integrated search field."""
        super().__init__(parent)
        
        # Internal state
        self._apply_action = None  # Reference to embedded apply button
        self._has_advanced_state = False  # Current state of advanced operator detection
        
        # Debouncing timer for advanced operator detection
        self._operator_detection_timer = QTimer()
        self._operator_detection_timer.setSingleShot(True)
        self._operator_detection_timer.timeout.connect(self._check_operators_debounced)
        self._last_text = ""
        
        self._init_ui()
        self._connect_signals()

        # Debug layout after initialization
        QTimer.singleShot(100, self._debug_layout_expansion)
    
    def _init_ui(self):
        """Initialize the UI with integrated search field layout."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)  # Rational spacing between components
        
        # Main text input field
        self._line_edit = QLineEdit()
        self._line_edit.setPlaceholderText("Search transactions... (e.g. coffee|tea, (coffee|tea) -decaf)")
        
        # Add decorative search icon (leading position)
        self._add_search_icon()
        
        # Style the line edit for integration
        self._line_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #333333;
                border-radius: 4px;
                background-color: #1E1E1E;
                color: white;
                padding: 6px 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3B8A45;
            }
        """)
        
        # Add line edit to layout (expandable)
        layout.addWidget(self._line_edit, 1)  # Stretch factor 1 for maximum expansion
        
        # External clear button (positioned outside text field)
        self._clear_button = create_integrated_clear_button()
        self._clear_button.setToolTip("Clear search")
        self._clear_button.hide()  # Hidden by default
        layout.addWidget(self._clear_button)
    
    def _add_search_icon(self):
        """Add decorative search icon to leading position."""
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            
            search_icon_path = icon_manager.get_toolbar_icon("search")
            search_icon = IconRenderer.load_icon(search_icon_path, QSize(14, 14))
            
            search_action = self._line_edit.addAction(search_icon, QLineEdit.LeadingPosition)
            search_action.setToolTip("Search")
            # No callback - decorative only
            
        except Exception as e:
            print(f"Warning: Could not load search icon: {e}")
    
    def _add_apply_button(self):
        """Add apply button to trailing position inside text field."""
        if self._apply_action is not None:
            print(f"DEBUG _add_apply_button: Apply button already exists, skipping")
            return  # Already added

        print(f"DEBUG _add_apply_button: Adding apply button to text field")
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer

            check_icon_path = icon_manager.get_toolbar_icon("check")
            check_icon = IconRenderer.load_icon(check_icon_path, QSize(16, 16))

            self._apply_action = self._line_edit.addAction(check_icon, QLineEdit.TrailingPosition)
            self._apply_action.setToolTip("Apply filter")
            self._apply_action.triggered.connect(self._on_apply_clicked)

            print(f"DEBUG _add_apply_button: Apply button successfully added")

        except Exception as e:
            print(f"ERROR _add_apply_button: Could not load apply icon: {e}")

    def _remove_apply_button(self):
        """Remove apply button from text field."""
        if self._apply_action is not None:
            print(f"DEBUG _remove_apply_button: Removing apply button from text field")
            self._line_edit.removeAction(self._apply_action)
            self._apply_action = None
            print(f"DEBUG _remove_apply_button: Apply button successfully removed")
        else:
            print(f"DEBUG _remove_apply_button: No apply button to remove")
    
    def _connect_signals(self):
        """Connect internal signals."""
        self._line_edit.textChanged.connect(self._on_text_changed)
        self._line_edit.returnPressed.connect(self._on_return_pressed)
        self._clear_button.clicked.connect(self._on_clear_clicked)
    
    def _on_text_changed(self, text):
        """Handle text change for live filtering with dynamic apply button."""
        self._last_text = text
        
        # Show/hide external clear button based on text content
        if text.strip():
            self._clear_button.show()
        else:
            self._clear_button.hide()
        
        # Check for advanced operators with debouncing
        if self._is_likely_simple_query(text):
            # Fast path for obviously simple queries
            self._has_advanced_state = False
            self.advanced_operators_detected.emit(False)
            self._remove_apply_button()  # Hide apply button for simple queries
            self.filter_changed.emit(text)  # Live filtering
        else:
            # Potentially complex query - use debounced detection
            self._operator_detection_timer.stop()
            self._operator_detection_timer.start(150)  # 150ms debounce
    
    def _on_return_pressed(self):
        """Handle Enter key press."""
        text = self._line_edit.text()
        self.filter_requested.emit(text)
    
    def _on_apply_clicked(self):
        """Handle apply button click."""
        text = self._line_edit.text()
        self.filter_requested.emit(text)
    
    def _on_clear_clicked(self):
        """Handle external clear button click."""
        self._line_edit.clear()
        # Text change will handle hiding clear button and apply button
    
    def _is_likely_simple_query(self, text):
        """Quick check for obviously simple queries to avoid unnecessary parsing."""
        if not text or len(text.strip()) < 2:
            return True

        # Enhanced check for obvious advanced operators
        advanced_chars = ['|', '(', ')', '&', '"', '+', '*', '~']
        has_advanced = any(char in text for char in advanced_chars)

        # Special case: check for dash operators
        has_dash_operator = (text.startswith('-') or ' -' in text or
                           text.endswith(' -') or '(-' in text)

        result = not (has_advanced or has_dash_operator)

        # Debug logging for troubleshooting
        if has_advanced or has_dash_operator:
            print(f"DEBUG _is_likely_simple_query: '{text}' -> COMPLEX (has_advanced={has_advanced}, has_dash={has_dash_operator})")
        else:
            print(f"DEBUG _is_likely_simple_query: '{text}' -> SIMPLE")

        return result
    
    def _check_operators_debounced(self):
        """Debounced check for advanced operators."""
        text = self._last_text
        print(f"DEBUG _check_operators_debounced: Processing '{text}' after debounce")

        has_advanced = self._has_advanced_operators(text)

        if has_advanced != self._has_advanced_state:
            print(f"DEBUG: Operator state changed from {self._has_advanced_state} to {has_advanced}")
            self._has_advanced_state = has_advanced
            self.advanced_operators_detected.emit(has_advanced)

            if has_advanced:
                print(f"DEBUG: Adding apply button for complex query: '{text}'")
                self._add_apply_button()  # Show apply button for complex queries
            else:
                print(f"DEBUG: Removing apply button for simple query: '{text}'")
                self._remove_apply_button()  # Hide for simple queries
                self.filter_changed.emit(text)  # Resume live filtering
        else:
            print(f"DEBUG: No operator state change needed (current: {self._has_advanced_state})")
    
    def _has_advanced_operators(self, text):
        """Enhanced check for advanced operators with comprehensive debugging."""
        if not text or not text.strip():
            print(f"DEBUG _has_advanced_operators: Empty text -> FALSE")
            return False

        # Comprehensive pattern detection
        patterns = [
            '|',    # OR operator
            '(',    # Group start
            ')',    # Group end
            '&',    # AND operator
            '"',    # Quoted strings
            '+',    # Additional operators
            '*',    # Wildcard
            '~',    # Fuzzy search
        ]

        # Check each pattern individually for detailed debugging
        found_patterns = []
        for pattern in patterns:
            if pattern in text:
                found_patterns.append(pattern)

        # Special case: dash operators (more complex logic)
        dash_operators = []
        if text.startswith('-'):
            dash_operators.append('leading-dash')
        if ' -' in text:
            dash_operators.append('space-dash')
        if text.endswith(' -'):
            dash_operators.append('trailing-dash')
        if '(-' in text:
            dash_operators.append('paren-dash')

        has_advanced = bool(found_patterns or dash_operators)

        # Comprehensive debug logging
        if has_advanced:
            print(f"DEBUG _has_advanced_operators: '{text}' -> TRUE")
            if found_patterns:
                print(f"  Found patterns: {found_patterns}")
            if dash_operators:
                print(f"  Found dash operators: {dash_operators}")
        else:
            print(f"DEBUG _has_advanced_operators: '{text}' -> FALSE (no patterns detected)")

        return has_advanced
    
    # Public API methods for compatibility
    
    def text(self):
        """Get the current text."""
        return self._line_edit.text()
    
    def setText(self, text):
        """Set the text."""
        self._line_edit.setText(text)
    
    def clear(self):
        """Clear the text."""
        self._line_edit.clear()

    def get_filter_text(self):
        """Get the current filter text (compatibility method)."""
        return self.text()

    def setPlaceholderText(self, text):
        """Set placeholder text."""
        self._line_edit.setPlaceholderText(text)
    
    def setFocus(self):
        """Set focus to the text input."""
        self._line_edit.setFocus()
    
    def blockSignals(self, block):
        """Block signals (for compatibility)."""
        self._line_edit.blockSignals(block)

    def _debug_layout_expansion(self):
        """Debug layout expansion behavior."""
        print(f"\n=== IntegratedSearchField Layout Debug ===")
        print(f"IntegratedSearchField size: {self.size()}")
        print(f"Line edit size: {self._line_edit.size()}")
        print(f"Clear button size: {self._clear_button.size()}")
        print(f"Clear button visible: {self._clear_button.isVisible()}")

        # Check parent layout
        parent = self.parent()
        if parent and hasattr(parent, 'layout') and parent.layout():
            layout = parent.layout()
            print(f"Parent layout type: {type(layout).__name__}")

            # Find our position in parent layout
            for i in range(layout.count()):
                item = layout.itemAt(i)
                if item and item.widget() == self:
                    stretch = layout.stretch(i)
                    print(f"IntegratedSearchField stretch factor: {stretch}")
                    break

            # Show all widgets in parent layout
            print(f"Parent layout widgets:")
            for i in range(layout.count()):
                item = layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    stretch = layout.stretch(i)
                    print(f"  {i}: {widget.__class__.__name__} - size: {widget.size()} - stretch: {stretch}")

        print(f"=== End Layout Debug ===\n")
