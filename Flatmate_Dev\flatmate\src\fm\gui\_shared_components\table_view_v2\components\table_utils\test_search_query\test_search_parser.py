"""
Test script for SearchQueryParser to verify package integration.
"""

from ..search_query_parser import SearchQueryParser, SearchQueryPreprocessor


def test_preprocessor():
    """Test the query preprocessor."""
    print("Testing SearchQueryPreprocessor...")
    
    preprocessor = SearchQueryPreprocessor()
    
    test_cases = [
        # (input, expected_output)
        ("coffee tea", "coffee tea"),  # Basic AND (implicit)
        ("coffee|tea", "coffee OR tea"),  # OR with pipe
        ("coffee/tea", "coffee OR tea"),  # OR with slash
        ("-decaf", "NOT decaf"),  # NOT with dash
        ("(coffee|tea) -decaf", "(coffee OR tea) NOT decaf"),  # Complex
        ('"coffee shop"', '"coffee shop"'),  # Quoted phrase
        ("coffee|tea hot", "coffee OR tea hot"),  # Mixed OR and AND
    ]
    
    for input_query, expected in test_cases:
        result = preprocessor.preprocess(input_query)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{input_query}' → '{result}' (expected: '{expected}')")
        if result != expected:
            print(f"    MISMATCH: got '{result}', expected '{expected}'")


def test_parser():
    """Test the main parser."""
    print("\nTesting SearchQueryParser...")
    
    parser = SearchQueryParser()
    
    if not parser.is_available():
        print("  ✗ luqum not available, skipping parser tests")
        return
    
    test_cases = [
        "coffee",
        "coffee tea",
        "coffee OR tea", 
        "coffee AND tea",
        "NOT decaf",
        "(coffee OR tea) AND NOT decaf",
        '"coffee shop"',
    ]
    
    for query in test_cases:
        try:
            ast = parser.parse(query)
            print(f"  ✓ Parsed '{query}' → {ast}")
        except Exception as e:
            print(f"  ✗ Failed to parse '{query}': {e}")


def test_evaluation():
    """Test query evaluation against sample data."""
    print("\nTesting query evaluation...")
    
    parser = SearchQueryParser()
    
    # Sample data
    test_data = [
        "Starbucks coffee shop purchase",
        "Tea house visit",
        "Decaf coffee from cafe",
        "Regular coffee and tea",
        "Gas station snacks",
    ]
    
    test_queries = [
        ("coffee", [0, 2, 3]),  # Should match items with "coffee"
        ("coffee OR tea", [0, 1, 2, 3]),  # Should match items with coffee or tea
        ("coffee -decaf", [0, 3]),  # Coffee but not decaf
        ('"coffee shop"', [0]),  # Exact phrase match
        ("gas", [4]),  # Simple word match
    ]
    
    for query, expected_indices in test_queries:
        print(f"\n  Testing query: '{query}'")
        matches = []
        for i, data in enumerate(test_data):
            if parser.evaluate(query, data):
                matches.append(i)
        
        status = "✓" if matches == expected_indices else "✗"
        print(f"    {status} Matched indices: {matches} (expected: {expected_indices})")
        
        if matches != expected_indices:
            print(f"    Expected matches:")
            for idx in expected_indices:
                print(f"      [{idx}] {test_data[idx]}")
            print(f"    Actual matches:")
            for idx in matches:
                print(f"      [{idx}] {test_data[idx]}")


def test_backward_compatibility():
    """Test backward compatibility with Phase 1 syntax."""
    print("\nTesting backward compatibility...")
    
    parser = SearchQueryParser()
    
    # Phase 1 syntax patterns
    test_cases = [
        ("coffee shop", "Starbucks coffee shop", True),  # AND logic
        ("coffee shop", "Tea house", False),  # AND logic
        ("-decaf", "Regular coffee", True),  # Exclude logic
        ("-decaf", "Decaf coffee", False),  # Exclude logic
        ("coffee -decaf", "Regular coffee", True),  # Combined
        ("coffee -decaf", "Decaf coffee", False),  # Combined
    ]
    
    for query, data, expected in test_cases:
        result = parser.evaluate(query, data)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{query}' vs '{data}' → {result} (expected: {expected})")


if __name__ == "__main__":
    print("Search Query Parser Test Suite")
    print("=" * 40)
    
    test_preprocessor()
    test_parser()
    test_evaluation()
    test_backward_compatibility()
    
    print("\nTest suite completed!")
