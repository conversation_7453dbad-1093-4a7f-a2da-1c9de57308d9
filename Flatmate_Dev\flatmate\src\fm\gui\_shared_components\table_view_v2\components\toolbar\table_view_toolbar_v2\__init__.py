"""
Table View Toolbar V2 - Modern Implementation

The default table view toolbar that ships with table views. This is the "home" toolbar
that provides the standard functionality most table views need.

Features:
1. New layout: [Column] [Search] "in:" [Dropdown] [Export]
2. Internal DB columns automatically filtered out
3. Smart search logic with performance optimization
4. Easy layout tweaking and maintenance
5. Consistent styling and behavior

This is the recommended toolbar for new table view implementations.

Usage:
    from .table_view_toolbar_v2 import TableViewToolbarV2

    toolbar = TableViewToolbarV2()
    toolbar.set_columns(columns, column_names)
    toolbar.filter_applied.connect(your_handler)
"""

from .table_view_toolbar_simple import TableViewToolbarSimple as TableViewToolbarV2, create_simple_toolbar as create_v2_toolbar

__all__ = [
    'TableViewToolbarV2',
    'create_v2_toolbar'
]
