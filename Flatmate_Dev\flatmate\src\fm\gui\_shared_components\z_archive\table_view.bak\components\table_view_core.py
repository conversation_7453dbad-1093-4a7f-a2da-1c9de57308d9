"""
Enhanced Table View System - Core Table Infrastructure

Provides advanced table components for displaying and interacting with tabular data.

Components:
- EnhancedTableModel: Data storage with editable/readonly column support
- EnhancedTableView: Advanced table display with filtering, sorting, export
- EnhancedTableWidget: Complete table solution with toolbar and controls

Features: Column management, filtering, export, pandas integration, config system support.

See enhanced_table_view_README.md for comprehensive documentation and usage examples.
"""

from typing import Dict, List, Optional, Union, Callable
import pandas as pd

from PySide6.QtCore import Qt, Signal, QModelIndex
from PySide6.QtGui import QColor, QAction
from PySide6.QtWidgets import (
    QTableView, QHeaderView, QAbstractItemView, QMenu
)

# Import components
from .table_utils.enhanced_table_model import EnhancedTableModel
from .table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel


class TableViewCore(QTableView):
    """
    Enhanced table view with advanced features:
    - Resizable columns with memory
    - Row highlighting
    - Column visibility toggle
    - Filtering
    - Export functionality
    - Custom context menu
    """
    
    # Signals
    row_selected = Signal(int)  # Emits row index when selected
    cell_edited = Signal(int, int, str)  # Emits row, col, new value when edited
    
    def __init__(self, parent=None):
        """Initialize the enhanced table view."""
        super().__init__(parent)
        
        # Set up the model and proxy
        self._model = EnhancedTableModel()
        self._proxy_model = EnhancedFilterProxyModel()
        self._proxy_model.setSourceModel(self._model)
        self.setModel(self._proxy_model)
        
        # Configure appearance
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.setSortingEnabled(True)
        
        # Configure horizontal header
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(False)  # Allow horizontal scrolling when content exceeds view
        header.setSectionsMovable(True)

        # Hide vertical header (row indices)
        self.verticalHeader().setVisible(False)
        
        # Connect signals
        self.selectionModel().selectionChanged.connect(self._on_selection_changed)
        self._model.itemChanged.connect(self._on_item_changed)
        
        # Set up context menu
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)
        
        # Track column widths
        self._column_widths = {}
        header.sectionResized.connect(self._on_section_resized)
    
    def _on_section_resized(self, logical_index, old_size, new_size):
        """Track column width changes."""
        self._column_widths[logical_index] = new_size

    def _auto_resize_columns_with_limit(self, max_chars=40):
        """Auto-resize columns to fit content with a maximum character limit.

        Args:
            max_chars: Maximum width in characters (default: 40)
        """
        header = self.horizontalHeader()
        char_width = self.fontMetrics().averageCharWidth()
        max_pixel_width = max_chars * char_width

        # First, resize all columns to contents
        self.resizeColumnsToContents()

        # Then apply the maximum width limit
        for col in range(self._model.columnCount()):
            current_width = header.sectionSize(col)
            if current_width > max_pixel_width:
                header.resizeSection(col, max_pixel_width)
    
    def _on_selection_changed(self, selected, deselected):
        """Handle selection changes."""
        indexes = selected.indexes()
        if indexes:
            # Get the source row index (accounting for sorting)
            proxy_index = indexes[0]
            source_index = self._proxy_model.mapToSource(proxy_index)
            self.row_selected.emit(source_index.row())
    
    def _on_item_changed(self, item):
        """Handle item edits."""
        row = item.row()
        col = item.column()
        value = item.text()
        self.cell_edited.emit(row, col, value)
    
    def _show_context_menu(self, position):
        """Show context menu with options."""
        menu = QMenu()
        
        # Copy action
        copy_action = QAction("Copy", self)
        copy_action.triggered.connect(self._copy_selection)
        menu.addAction(copy_action)
        
        # Copy row action
        copy_row_action = QAction("Copy Row", self)
        copy_row_action.triggered.connect(self._copy_row)
        menu.addAction(copy_row_action)
        
        # Column visibility submenu
        column_menu = menu.addMenu("Column Visibility")
        for col in range(self._model.columnCount()):
            col_name = self._model.headerData(col, Qt.Horizontal)
            action = QAction(str(col_name), self)
            action.setCheckable(True)
            action.setChecked(not self.isColumnHidden(col))
            action.triggered.connect(lambda checked, column=col: self.setColumnHidden(column, not checked))
            column_menu.addAction(action)
        
        # Export submenu
        export_menu = menu.addMenu("Export")
        export_csv_action = QAction("Export to CSV", self)
        export_csv_action.triggered.connect(lambda: self._export_data("csv"))
        export_menu.addAction(export_csv_action)
        
        export_excel_action = QAction("Export to Excel", self)
        export_excel_action.triggered.connect(lambda: self._export_data("excel"))
        export_menu.addAction(export_excel_action)
        
        menu.exec(self.mapToGlobal(position))
    
    def _copy_selection(self):
        """Copy selected cells to clipboard."""
        selection = self.selectedIndexes()
        if not selection:
            return
            
        # Sort by row, then column
        selection.sort(key=lambda idx: (idx.row(), idx.column()))
        
        # Group by rows
        rows = {}
        for index in selection:
            row = index.row()
            if row not in rows:
                rows[row] = []
            rows[row].append(index.data())
        
        # Format as tab-separated text
        text = "\n".join("\t".join(str(cell) for cell in row) for row in rows.values())
        
        # Copy to clipboard
        from PySide6.QtGui import QGuiApplication
        QGuiApplication.clipboard().setText(text)
    
    def _copy_row(self):
        """Copy entire row to clipboard."""
        selection = self.selectedIndexes()
        if not selection:
            return
            
        # Get the row of the first selected cell
        row = selection[0].row()
        
        # Get all cells in this row
        row_data = []
        for col in range(self.model().columnCount()):
            index = self.model().index(row, col)
            row_data.append(str(index.data()))
        
        # Format as tab-separated text
        text = "\t".join(row_data)
        
        # Copy to clipboard
        from PySide6.QtGui import QGuiApplication
        QGuiApplication.clipboard().setText(text)
    
    def _export_data(self, format_type):
        """Export data to file."""
        from PySide6.QtWidgets import QFileDialog
        
        # Get DataFrame from model
        df = self.get_dataframe()
        
        if format_type == "csv":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to CSV", "", "CSV Files (*.csv)")
            if file_path:
                df.to_csv(file_path, index=False)
        elif format_type == "excel":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to Excel", "", "Excel Files (*.xlsx)")
            if file_path:
                df.to_excel(file_path, index=False)
    
    def set_dataframe(self, df: pd.DataFrame):
        """Set the table data from a pandas DataFrame."""
        self._model.set_dataframe(df)

        # Auto-resize columns to contents with maximum width limit
        self._auto_resize_columns_with_limit()

        # Restore saved column widths if available (this overrides auto-sizing)
        header = self.horizontalHeader()
        for col, width in self._column_widths.items():
            if col < self._model.columnCount():
                header.resizeSection(col, width)
    
    def get_dataframe(self) -> pd.DataFrame:
        """Get the current data as a pandas DataFrame."""
        return self._model.get_dataframe()
    
    def set_editable_columns(self, columns: List[Union[int, str]]):
        """Set which columns should be editable."""
        # Convert column names to indices if needed
        col_indices = []
        for col in columns:
            if isinstance(col, str):
                for i in range(self._model.columnCount()):
                    if self._model.headerData(i, Qt.Horizontal) == col:
                        col_indices.append(i)
                        break
            else:
                col_indices.append(col)
        
        self._model.set_editable_columns(col_indices)
    
    def set_column_filter(self, column: Union[int, str], pattern: str):
        """Set filter for a specific column or all columns."""
        print(f"DEBUG set_column_filter: column='{column}', pattern='{pattern}'")

        # Handle special "all_columns" case
        if column == "all_columns":
            print(f"DEBUG: Setting filter for all columns with pattern '{pattern}'")
            self._proxy_model.set_column_filter("all_columns", pattern)
            return

        # Convert column name to index if needed
        col_idx = column
        if isinstance(column, str):
            print(f"DEBUG: Converting column name '{column}' to index")
            for i in range(self._model.columnCount()):
                header_name = self._model.headerData(i, Qt.Horizontal)
                print(f"DEBUG: Checking header {i}: '{header_name}' vs '{column}'")
                # Try exact match first, then case-insensitive match
                if header_name == column or (header_name and header_name.lower() == column.lower()):
                    col_idx = i
                    print(f"DEBUG: Found match! Column '{column}' -> index {i}")
                    break

            # If still not found, log the issue for debugging
            if col_idx == column:  # Still a string, not converted to index
                print(f"DEBUG: Could not find column '{column}' in headers. Available headers:")
                for i in range(self._model.columnCount()):
                    header = self._model.headerData(i, Qt.Horizontal)
                    print(f"  {i}: '{header}'")
                return  # Don't set filter if column not found

        print(f"DEBUG: Setting filter on column index {col_idx} with pattern '{pattern}'")
        self._proxy_model.set_column_filter(col_idx, pattern)
    
    def clear_filters(self):
        """Clear all filters."""
        self._proxy_model.clear_filters()
    
    def highlight_row(self, row: int, color: QColor = QColor(230, 230, 255)):
        """Highlight a specific row."""
        for col in range(self._model.columnCount()):
            item = self._model.item(row, col)
            if item:
                item.setBackground(color)
    
    def set_display_columns(self, columns, column_names=None):
        """Set which columns to display and their display names.
        
        Args:
            columns: List of database column names to display
            column_names: Dictionary mapping database column names to display names
        """
        # Store the database column names in the model
        self._model._display_columns = columns
        
        # Set the headers to display names
        headers = []
        for col in columns:
            display_name = col
            if column_names and col in column_names:
                display_name = column_names[col]
            headers.append(display_name)
        
        # Set the horizontal header labels
        self._model.setHorizontalHeaderLabels(headers)
        
        # Show only the specified columns
        for col_idx in range(self._model.columnCount()):
            # Get the original column name
            orig_col_name = self._model.headerData(col_idx, Qt.Horizontal, Qt.UserRole)
            if not orig_col_name:  # If not stored in UserRole, use the display name
                orig_col_name = self._model.headerData(col_idx, Qt.Horizontal)
            
            # Hide if not in the display columns
            self.setColumnHidden(col_idx, orig_col_name not in columns)

    def set_column_widths(self, width_map: Dict[str, int], max_chars=40):
        """Set custom column widths with auto-fit and maximum limit.

        Args:
            width_map: Dictionary mapping column names to widths (in characters)
            max_chars: Maximum width in characters for auto-fitted columns (default: 40)
        """
        if not width_map:
            # If no width map provided, just auto-fit with limit
            self._auto_resize_columns_with_limit(max_chars)
            return

        header = self.horizontalHeader()
        char_width = self.fontMetrics().averageCharWidth()
        max_pixel_width = max_chars * char_width

        # First auto-resize all columns to content
        self.resizeColumnsToContents()

        for col_idx in range(self._model.columnCount()):
            # Get column name
            col_name = self._model.headerData(col_idx, Qt.Horizontal)

            if col_name in width_map:
                # Use configured width, but respect maximum limit
                configured_width = width_map[col_name] * char_width
                final_width = min(configured_width, max_pixel_width)
                header.resizeSection(col_idx, final_width)
            else:
                # For columns not in width_map, apply max limit to auto-fitted width
                current_width = header.sectionSize(col_idx)
                if current_width > max_pixel_width:
                    header.resizeSection(col_idx, max_pixel_width)

