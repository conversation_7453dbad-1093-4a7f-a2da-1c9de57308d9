"""
Filter Group

Combines filter-related components into a cohesive group.
Contains all the component classes it uses for better cohesion.
"""

from PySide6.QtCore import Signal, QTimer, QSize
from PySide6.QtWidgets import QWidget, QHBoxLayout, QComboBox, QLineEdit, QPushButton, QToolButton
from PySide6.QtGui import QIcon
from pathlib import Path
from fm.gui._shared_components.toolbar import BaseToolbarButton
from ..table_view_toolbar_v1.integrated_search_field import IntegratedSearchField


class ColumnSelector(QComboBox):
    """Dropdown for selecting which column to filter."""

    column_changed = Signal(object)  # Emits column index or "all_columns" when selection changes

    def __init__(self, parent=None):
        """Initialize the column selector."""
        super().__init__(parent)
        self.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.setMinimumWidth(120)  # Ensure reasonable minimum width
        self.setMaximumWidth(200)  # Prevent it from getting too wide
        self.setToolTip("Select column to search in")

        # Apply consistent styling
        self.setStyleSheet("""
            QComboBox {
                background-color: #2A2A2A;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 4px 8px;
                min-height: 20px;
            }
            QComboBox:hover {
                border-color: #3B8A45;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid white;
                margin-right: 4px;
            }
        """)

        # Connect signal
        self.currentIndexChanged.connect(self._on_selection_changed)

    def _on_selection_changed(self, _index):
        """Handle selection changes."""
        column_data = self.currentData()
        print(f"DEBUG ColumnSelector._on_selection_changed: column_data='{column_data}'")
        if column_data is not None:
            # Safety check for signal existence (in case of reload issues)
            if hasattr(self, 'column_changed'):
                self.column_changed.emit(column_data)
            else:
                print("WARNING: column_changed signal not found!")

    def set_columns(self, columns, column_names=None):
        """Set available columns for search.

        Only shows visible columns and defaults to 'Details' as the primary text search column.
        Removes 'All Columns' option as it's too performance-heavy to parse.
        Filters out system columns that shouldn't be visible to users.

        Args:
            columns: List of visible column identifiers
            column_names: Optional dict mapping column IDs to display names
        """
        print(f"DEBUG ColumnSelector.set_columns: columns={columns}")
        print(f"DEBUG ColumnSelector.set_columns: column_names={column_names}")

        self.clear()

        # Filter out system columns that shouldn't be visible to users
        filtered_columns = self._filter_user_visible_columns(columns)
        print(f"DEBUG ColumnSelector: Filtered columns: {filtered_columns}")

        # Only add user-visible columns (no "All Columns" option)
        for i, col in enumerate(filtered_columns):
            display_name = column_names.get(col, col) if column_names else col
            print(f"DEBUG: Adding item '{display_name}' with data '{col}'")
            # Store display name as data for easier lookup
            self.addItem(str(display_name), col)

        # Set default to 'Details' if available, otherwise first column
        # Try to find Details by both text and data
        details_index = -1

        # First try to find by item text (display name)
        for i in range(self.count()):
            item_text = self.itemText(i)
            if item_text.lower() in ['details', 'detail']:
                details_index = i
                print(f"DEBUG: Found Details by text '{item_text}' at index: {details_index}")
                break

        # If not found by text, try by data
        if details_index < 0:
            for variant in ['Details', 'details', 'Detail', 'detail']:
                details_index = self.findData(variant)
                if details_index >= 0:
                    print(f"DEBUG: Found Details by data '{variant}' at index: {details_index}")
                    break

        if details_index >= 0:
            self.setCurrentIndex(details_index)
            print(f"DEBUG: Set current index to {details_index} ({self.itemText(details_index)})")
        elif self.count() > 0:
            # Fallback to first column if details not found
            self.setCurrentIndex(0)
            print(f"DEBUG: Details not found, defaulting to first column: {self.itemText(0)}")
            print("DEBUG: Available columns:")
            for i in range(self.count()):
                print(f"  {i}: '{self.itemText(i)}' (data: '{self.itemData(i)}')")
        else:
            print(f"DEBUG: No columns available for search")

    def get_selected_column(self):
        """Get the currently selected column index."""
        return self.currentData()

    def set_selected_column(self, column_name: str):
        """Set the selected column by name.

        Args:
            column_name: Name of the column to select (e.g., "details", "all_columns")
        """
        index = self.findData(column_name)
        if index >= 0:
            self.setCurrentIndex(index)
            print(f"DEBUG: Set column selector to '{column_name}' at index {index}")
        else:
            print(f"WARNING: Column '{column_name}' not found in selector")

    def _filter_user_visible_columns(self, columns):
        """Filter out system columns that shouldn't be visible to users.

        Based on the test results, these system columns should be hidden:
        - DB UID (internal database identifier)
        - Source UID (bank-provided identifier, technical)
        - Hash (internal duplicate detection)
        - Is Deleted (system flag)
        - Import Date (system metadata)
        - Modified Date (system metadata)

        But these should remain visible:
        - Unique ID (user-meaningful identifier)

        Args:
            columns: List of column names (display names or db names)

        Returns:
            List of user-visible column names
        """
        # Define system columns that should be hidden from users
        # Based on the Columns enum 'db_system' group
        system_columns_to_hide = {
            # DB names (from db_system group)
            'db_uid', 'source_uid', 'hash', 'is_deleted', 'import_date', 'modified_date',
            # Display names (from db_system group)
            'DB UID', 'Source UID', 'Hash', 'Is Deleted', 'Import Date', 'Modified Date',
        }

        # Filter out system columns (case-insensitive)
        filtered = []
        for col in columns:
            col_lower = str(col).lower()

            # Check if this is a system column that should be hidden
            should_hide = False
            for sys_col in system_columns_to_hide:
                if col_lower == sys_col.lower():
                    should_hide = True
                    print(f"DEBUG: Filtering out system column: {col}")
                    break

            if not should_hide:
                filtered.append(col)

        return filtered


class FilterInput(QWidget):
    """Enhanced text input field with search icon and clear button for entering filter patterns."""

    filter_changed = Signal(str)  # Emits filter text as user types (live filtering)
    filter_requested = Signal(str)  # Emits filter text when Enter is pressed (legacy)
    advanced_operators_detected = Signal(bool)  # Emits True when advanced operators are detected

    def __init__(self, parent=None):
        """Initialize the filter input with embedded icons."""
        super().__init__(parent)

        # Debouncing timer for advanced operator detection
        self._operator_detection_timer = QTimer()
        self._operator_detection_timer.setSingleShot(True)
        self._operator_detection_timer.timeout.connect(self._check_operators_debounced)
        self._last_text = ""

        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """Initialize the UI with search icon, input field, and clear button."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create the actual line edit
        self._line_edit = QLineEdit()
        self._line_edit.setPlaceholderText("Search transactions... (e.g. coffee|tea, (coffee|tea) -decaf)")

        # Load icons using the icon manager
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer

            # Search icon (left side)
            search_icon_path = icon_manager.get_toolbar_icon("search")
            self._search_icon = IconRenderer.load_icon(search_icon_path, QSize(16, 16))

            # Clear icon (right side)
            clear_icon_path = icon_manager.get_toolbar_icon("clear")
            self._clear_icon = IconRenderer.load_icon(clear_icon_path, QSize(16, 16))

        except Exception as e:
            print(f"Warning: Could not load toolbar icons: {e}")
            self._search_icon = QIcon()
            self._clear_icon = QIcon()

        # Search icon button (non-clickable, just visual)
        self._search_button = QToolButton()
        self._search_button.setIcon(self._search_icon)
        self._search_button.setIconSize(QSize(16, 16))
        self._search_button.setEnabled(False)  # Just decorative
        self._search_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                padding: 4px;
            }
        """)

        # Clear button (clickable, hidden by default)
        self._clear_button = QToolButton()
        self._clear_button.setIcon(self._clear_icon)
        self._clear_button.setIconSize(QSize(16, 16))
        self._clear_button.setToolTip("Clear search")
        self._clear_button.hide()  # Hidden by default
        self._clear_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                padding: 4px;
                border-radius: 2px;
            }
            QToolButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)

        # Create container widget with proper styling
        container = QWidget()
        container.setStyleSheet("""
            QWidget {
                border: 1px solid #333333;
                border-radius: 4px;
                background-color: #1E1E1E;
            }
            QWidget:focus-within {
                border-color: #3B8A45;
            }
        """)

        # Container layout with minimal padding for maximum expansion
        container_layout = QHBoxLayout(container)
        container_layout.setContentsMargins(1, 1, 1, 1)  # Minimal padding
        container_layout.setSpacing(1)  # Minimal spacing

        # Style the line edit to remove its own border and minimize padding
        self._line_edit.setStyleSheet("""
            QLineEdit {
                border: none;
                background: transparent;
                color: white;
                padding: 2px;  /* Minimal padding to maximize text space */
            }
        """)

        # Add widgets to container
        container_layout.addWidget(self._search_button)
        container_layout.addWidget(self._line_edit, 1)  # Expand to fill space
        container_layout.addWidget(self._clear_button)

        # Add container to main layout
        layout.addWidget(container)

    def _connect_signals(self):
        """Connect internal signals."""
        self._line_edit.textChanged.connect(self._on_text_changed)
        self._line_edit.returnPressed.connect(self._on_return_pressed)
        self._clear_button.clicked.connect(self._on_clear_clicked)

    def _on_text_changed(self, text):
        """Handle text change for live filtering with debounced operator detection."""
        self._last_text = text

        # Show/hide clear button based on text content
        if text.strip():
            self._clear_button.show()
        else:
            self._clear_button.hide()

        # For simple queries, do immediate live filtering
        # For complex queries, use debounced operator detection to avoid UI freezes
        if self._is_likely_simple_query(text):
            # Fast path for obviously simple queries
            self.advanced_operators_detected.emit(False)
            self._update_visual_feedback(False)
            self.filter_changed.emit(text)
        else:
            # Potentially complex query - use debounced detection
            self._operator_detection_timer.stop()
            self._operator_detection_timer.start(150)  # 150ms debounce

    def _is_likely_simple_query(self, text):
        """Quick check for obviously simple queries to enable fast path."""
        if not text or not text.strip():
            return True

        # If it contains any of these characters, it's definitely not simple
        complex_chars = ['|', '/', '(', ')', '"']
        return not any(char in text for char in complex_chars)

    def _check_operators_debounced(self):
        """Debounced operator detection to prevent UI freezes during typing."""
        text = self._last_text
        has_advanced = self._has_advanced_operators(text)
        self.advanced_operators_detected.emit(has_advanced)
        self._update_visual_feedback(has_advanced)

        # Only emit filter_changed for live filtering if no advanced operators
        if not has_advanced:
            self.filter_changed.emit(text)

    def _on_clear_clicked(self):
        """Handle clear button click."""
        self.clear_filter()

    def _update_visual_feedback(self, has_advanced):
        """Update visual feedback based on advanced operator detection.

        Args:
            has_advanced: True if advanced operators are detected
        """
        # Find the container widget to update its styling
        container = self.findChild(QWidget)
        if container:
            if has_advanced:
                # Add subtle visual indicator for advanced query mode
                container.setStyleSheet("""
                    QWidget {
                        border: 2px solid #3B8A45;  /* Green border to indicate advanced mode */
                        border-radius: 4px;
                        background-color: #1E1E1E;
                    }
                """)
                # Update placeholder to indicate advanced mode
                self._line_edit.setPlaceholderText("Advanced search mode - press Enter or click Apply to filter")
            else:
                # Reset to normal styling
                container.setStyleSheet("""
                    QWidget {
                        border: 1px solid #333333;
                        border-radius: 4px;
                        background-color: #1E1E1E;
                    }
                    QWidget:focus-within {
                        border-color: #3B8A45;
                    }
                """)
                # Reset to normal placeholder
                self._line_edit.setPlaceholderText("Search transactions... (e.g. coffee|tea, (coffee|tea) -decaf)")

    def _has_advanced_operators(self, text):
        """Check if text contains advanced operators that should disable live filtering.

        Uses the search query parser as the single source of truth for this decision.
        This ensures consistency between the UI behavior and the actual parsing logic.

        Args:
            text: Input text to check

        Returns:
            True if advanced operators are detected (i.e., NOT a simple query)
        """
        if not text or not text.strip():
            return False

        # Import the search parser to use its logic
        try:
            from ...table_utils.search_query_parser import get_search_parser
            parser = get_search_parser()
            # If it's NOT a simple query, then it has advanced operators
            return not parser._is_simple_query(text)
        except ImportError:
            # Fallback to basic detection if parser not available
            return self._fallback_advanced_detection(text)

    def _fallback_advanced_detection(self, text):
        """Fallback advanced operator detection if search parser not available."""
        # Check for complex operators that require package parsing
        advanced_indicators = [
            '|',      # OR operator (pipe)
            '/',      # OR operator (slash)
            '(',      # Opening parenthesis
            ')',      # Closing parenthesis
            '"',      # Quoted phrases
        ]

        # Check for character-based operators
        for indicator in advanced_indicators:
            if indicator in text:
                return True

        # Check for keyword operators (case-insensitive)
        text_upper = text.upper()
        keyword_operators = ['OR', 'AND', 'NOT']
        for keyword in keyword_operators:
            # Use word boundaries to avoid false positives
            import re
            if re.search(r'\b' + keyword + r'\b', text_upper):
                return True

        return False

    def _on_return_pressed(self):
        """Handle Enter key press (legacy support)."""
        self.filter_requested.emit(self._line_edit.text())

    def get_filter_text(self):
        """Get the current filter text."""
        return self._line_edit.text()

    def clear_filter(self):
        """Clear the filter input."""
        self._line_edit.clear()

    def text(self):
        """Get the current text (for compatibility)."""
        return self._line_edit.text()

    def setText(self, text):
        """Set the text (for compatibility)."""
        self._line_edit.setText(text)

    def blockSignals(self, block):
        """Block signals (for compatibility)."""
        self._line_edit.blockSignals(block)


class ApplyButton(BaseToolbarButton):
    """Button for applying filters with check icon."""

    apply_requested = Signal()  # Emitted when button is clicked

    def __init__(self, parent=None):
        """Initialize the apply button using BaseToolbarButton."""
        super().__init__(
            icon_name="check",
            tooltip="Apply filter",
            style_variant="primary",
            parent=parent
        )
        self.clicked.connect(self.apply_requested)





class FilterGroup(QWidget):
    """Group of filter controls working together."""

    # Signals for external communication
    filter_applied = Signal(object, str)  # column (int or str), pattern
    filters_cleared = Signal()

    def __init__(self, parent=None, live_filtering=True):
        """Initialize the filter group.

        Args:
            parent: Parent widget
            live_filtering: If True, filters as user types. If False, requires Apply button.
        """
        super().__init__(parent)
        self.live_filtering = live_filtering
        self._live_filtering_enabled = live_filtering  # Track current state
        self._has_advanced_operators = False  # Track if advanced operators are present

        # Set CSS class for styling
        self.setObjectName("FilterGroup")

        self._init_ui()
        self._connect_signals()

        # Debug layout after initialization
        QTimer.singleShot(200, self._debug_filter_group_layout)
    
    def _init_ui(self):
        """Initialize the UI components with optimized layout."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)  # Tight spacing between components

        # Create search container with zero padding to maximize expansion
        search_container = QWidget()
        search_container.setObjectName("SearchContainer")
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(4)

        # Search icon (replaces "Search:" text label for space optimization)
        self.search_icon = BaseToolbarButton(
            icon_name="search",
            tooltip="Search",
            style_variant="embedded"
        )
        self.search_icon.setEnabled(False)  # Decorative only
        search_layout.addWidget(self.search_icon)

        # Column selector
        self.column_selector = ColumnSelector()
        search_layout.addWidget(self.column_selector)

        # Integrated search field (expandable to fill available space)
        # This replaces both FilterInput and ApplyButton with integrated solution
        self.filter_input = IntegratedSearchField()
        search_layout.addWidget(self.filter_input, 1)  # Stretch factor of 1 to expand

        # Note: Apply button is now embedded inside the IntegratedSearchField
        # No separate apply button needed

        # Add search container to main layout with maximum stretch
        layout.addWidget(search_container, 1)  # Give search container maximum priority
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Connect advanced operator detection
        self.filter_input.advanced_operators_detected.connect(self._on_advanced_operators_detected)

        # Connect IntegratedSearchField signals
        # Live filtering: apply filter as user types (for simple queries)
        self.filter_input.filter_changed.connect(self._apply_filter_live)

        # Apply button (embedded) and Enter key: immediate application
        self.filter_input.filter_requested.connect(self._apply_filter)

        # Note: IntegratedSearchField handles apply button show/hide internally
        # No separate apply button to connect

        # Reapply filter when column selection changes (for live filtering)
        if hasattr(self.column_selector, 'column_changed'):
            self.column_selector.column_changed.connect(self._on_column_changed)

    def _on_advanced_operators_detected(self, has_advanced):
        """Handle detection of advanced operators in the search input.

        When advanced operators are detected:
        - Disable live filtering
        - Show apply button
        - Require explicit apply action

        When no advanced operators:
        - Enable live filtering (if originally enabled)
        - Hide apply button
        - Resume live filtering

        Args:
            has_advanced: True if advanced operators are detected
        """
        self._has_advanced_operators = has_advanced

        if self.live_filtering:  # Only manage live filtering state
            if has_advanced:
                # Disable live filtering for advanced queries
                # Apply button is handled internally by IntegratedSearchField
                self._live_filtering_enabled = False
                print("DEBUG: Advanced operators detected - live filtering disabled, apply button shown internally")
            else:
                # Enable live filtering for simple queries
                # Apply button is hidden internally by IntegratedSearchField
                self._live_filtering_enabled = True
                print("DEBUG: Simple query detected - live filtering enabled, apply button hidden internally")

    def _debug_filter_group_layout(self):
        """Debug FilterGroup layout behavior."""
        print(f"\n=== FilterGroup Layout Debug ===")
        print(f"FilterGroup size: {self.size()}")

        # Check main layout
        layout = self.layout()
        if layout:
            print(f"Main layout type: {type(layout).__name__}")
            print(f"Main layout widgets:")
            for i in range(layout.count()):
                item = layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    stretch = layout.stretch(i)
                    print(f"  {i}: {widget.__class__.__name__} - size: {widget.size()} - stretch: {stretch}")

        # Check search container layout
        if hasattr(self, 'filter_input'):
            print(f"IntegratedSearchField size: {self.filter_input.size()}")

        print(f"=== End FilterGroup Debug ===\n")

    # todo this funciton should be dprctd with new live search function
    def _apply_filter(self):
        """Apply the current filter (traditional method)."""
        column = self.column_selector.get_selected_column()
        pattern = self.filter_input.get_filter_text()
        print(f"DEBUG _apply_filter: column='{column}', pattern='{pattern}'")
      
        if column is not None:
            self.filter_applied.emit(column, pattern)

    def _apply_filter_live(self, pattern):
        """Apply filter as user types (live filtering).

        Only applies if live filtering is currently enabled (no advanced operators).
        """
        # Only apply live filtering if it's currently enabled
        if not self._live_filtering_enabled:
            print(f"DEBUG _apply_filter_live: Skipping live filter - advanced operators detected")
            return

        column = self.column_selector.get_selected_column()
        print(f"DEBUG _apply_filter_live: column='{column}', pattern='{pattern}'")

        if column is not None:
            self.filter_applied.emit(column, pattern)

    def _on_column_changed(self, column):
        """Handle column selection change - reapply current filter to new column."""
        if self.live_filtering and self._live_filtering_enabled:
            current_pattern = self.filter_input.get_filter_text()
            if current_pattern:  # Only reapply if there's a pattern
                print(f"DEBUG _on_column_changed: Reapplying pattern '{current_pattern}' to column '{column}'")
                self.filter_applied.emit(column, current_pattern)


    
    def set_columns(self, columns, column_names=None):
        """Set available columns for filtering.

        Args:
            columns: List of column identifiers
            column_names: Optional dict mapping column IDs to display names
        """
        self.column_selector.set_columns(columns, column_names)

    def set_filter_state(self, column: str, pattern: str):
        """Set filter state externally (for persistence restore).

        Args:
            column: Column name to select
            pattern: Filter pattern to set
        """
        print(f"DEBUG: FilterGroup.set_filter_state called with column='{column}', pattern='{pattern}'")

        # Set column selector
        if hasattr(self.column_selector, 'set_selected_column'):
            self.column_selector.set_selected_column(column)
        else:
            print("WARNING: ColumnSelector doesn't have set_selected_column method")

        # Set filter text without triggering change signals
        self.filter_input.blockSignals(True)
        self.filter_input.setText(pattern)
        self.filter_input.blockSignals(False)

        # Manually emit the filter_applied signal to ensure the filter is actually applied
        if pattern:  # Only emit if there's a pattern to apply
            print(f"DEBUG: Emitting filter_applied signal for restoration")
            self.filter_applied.emit(column, pattern)

    def get_filter_state(self) -> tuple[str, str]:
        """Get current filter state.

        Returns:
            Tuple of (column, pattern)
        """
        column = self.column_selector.get_selected_column() if hasattr(self.column_selector, 'get_selected_column') else "details"
        pattern = self.filter_input.text()
        return column, pattern
