"""
Enhanced Table View System - Core Table Infrastructure

Provides advanced table components for displaying and interacting with tabular data.

Components:
- EnhancedTableModel: Data storage with editable/readonly column support
- EnhancedTableView: Advanced table display with filtering, sorting, export
- EnhancedTableWidget: Complete table solution with toolbar and controls

Features: Column management, filtering, export, pandas integration, config system support.

See README.md for comprehensive documentation and usage examples.
"""

from typing import Dict, List, Optional, Union, Callable
import pandas as pd

from PySide6.QtCore import Qt, Signal, QSortFilterProxyModel, QModelIndex
from PySide6.QtGui import QStandardItemModel, QStandardItem, QColor, QAction
from PySide6.QtWidgets import (
    QTableView, QHeaderView, QAbstractItemView, QMenu,
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLineEdit,
    QPushButton, QLabel, QCheckBox, QFrame
)


class EnhancedTableModel(QStandardItemModel):
    """Enhanced table model with additional features for data handling."""
    
    def __init__(self, parent=None):
        """Initialize the enhanced table model."""
        super().__init__(parent)
        self._editable_columns = set()
        self._column_types = {}
        self._original_data = None
        self._display_columns = None
    
    def set_editable_columns(self, columns: List[int]):
        """Set which columns should be editable."""
        self._editable_columns = set(columns)
    
    def set_column_types(self, column_types: Dict[int, str]):
        """Set column data types for proper sorting and filtering."""
        self._column_types = column_types
    
    def flags(self, index):
        """Return item flags based on column editability."""
        flags = super().flags(index)
        if index.column() in self._editable_columns:
            return flags | Qt.ItemIsEditable
        return flags & ~Qt.ItemIsEditable
    
    def set_dataframe(self, df: pd.DataFrame):
        """Set the model data from a pandas DataFrame."""
        self._original_data = df.copy()
        
        # Clear existing data
        self.clear()
        
        # Set headers
        self.setHorizontalHeaderLabels([str(col) for col in df.columns])
        
        # Add data rows
        for row_idx, row in df.iterrows():
            items = []
            for col_idx, value in enumerate(row):
                item = QStandardItem(str(value))
                # Store original value as user data for sorting
                item.setData(value, Qt.UserRole)
                items.append(item)
            self.appendRow(items)
    
    def get_dataframe(self) -> pd.DataFrame:
        """Get the current data as a pandas DataFrame."""
        if self._original_data is None:
            return pd.DataFrame()
        
        # Create a copy of the original DataFrame
        df = self._original_data.copy()
        
        # Update with edited values
        for row in range(self.rowCount()):
            for col in range(self.columnCount()):
                if col in self._editable_columns:
                    item = self.item(row, col)
                    if item:
                        df.iloc[row, col] = item.text()
        
        return df


class EnhancedFilterProxyModel(QSortFilterProxyModel):
    """Enhanced filter proxy model with per-column filtering."""
    
    def __init__(self, parent=None):
        """Initialize the enhanced filter proxy model."""
        super().__init__(parent)
        self._column_filters = {}
    
    def set_column_filter(self, column: int, pattern: str):
        """Set filter for a specific column."""
        if not pattern:
            if column in self._column_filters:
                del self._column_filters[column]
        else:
            self._column_filters[column] = pattern
        self.invalidateFilter()
    
    def clear_filters(self):
        """Clear all filters."""
        self._column_filters.clear()
        self.invalidateFilter()
    
    def filterAcceptsRow(self, source_row, source_parent):
        """Check if row matches all column filters."""
        model = self.sourceModel()
        
        # If no filters, accept all rows
        if not self._column_filters:
            return True
        
        # Check each column filter
        for column, pattern in self._column_filters.items():
            index = model.index(source_row, column, source_parent)
            if not index.isValid():
                continue
                
            data = model.data(index, Qt.DisplayRole)
            if data is None:
                continue
                
            if pattern.lower() not in str(data).lower():
                return False
        
        return True


class EnhancedTableView(QTableView):
    """
    Enhanced table view with advanced features:
    - Resizable columns with memory
    - Row highlighting
    - Column visibility toggle
    - Filtering
    - Export functionality
    - Custom context menu
    """
    
    # Signals
    row_selected = Signal(int)  # Emits row index when selected
    cell_edited = Signal(int, int, str)  # Emits row, col, new value when edited
    
    def __init__(self, parent=None):
        """Initialize the enhanced table view."""
        super().__init__(parent)
        
        # Set up the model and proxy
        self._model = EnhancedTableModel()
        self._proxy_model = EnhancedFilterProxyModel()
        self._proxy_model.setSourceModel(self._model)
        self.setModel(self._proxy_model)
        
        # Configure appearance
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.setSortingEnabled(True)
        
        # Configure header
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)
        header.setSectionsMovable(True)
        
        # Connect signals
        self.selectionModel().selectionChanged.connect(self._on_selection_changed)
        self._model.itemChanged.connect(self._on_item_changed)
        
        # Set up context menu
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)
        
        # Track column widths
        self._column_widths = {}
        header.sectionResized.connect(self._on_section_resized)
    
    def _on_section_resized(self, logical_index, old_size, new_size):
        """Track column width changes."""
        self._column_widths[logical_index] = new_size
    
    def _on_selection_changed(self, selected, deselected):
        """Handle selection changes."""
        indexes = selected.indexes()
        if indexes:
            # Get the source row index (accounting for sorting)
            proxy_index = indexes[0]
            source_index = self._proxy_model.mapToSource(proxy_index)
            self.row_selected.emit(source_index.row())
    
    def _on_item_changed(self, item):
        """Handle item edits."""
        row = item.row()
        col = item.column()
        value = item.text()
        self.cell_edited.emit(row, col, value)
    
    def _show_context_menu(self, position):
        """Show context menu with options."""
        menu = QMenu()
        
        # Copy action
        copy_action = QAction("Copy", self)
        copy_action.triggered.connect(self._copy_selection)
        menu.addAction(copy_action)
        
        # Copy row action
        copy_row_action = QAction("Copy Row", self)
        copy_row_action.triggered.connect(self._copy_row)
        menu.addAction(copy_row_action)
        
        # Column visibility submenu
        column_menu = menu.addMenu("Column Visibility")
        for col in range(self._model.columnCount()):
            col_name = self._model.headerData(col, Qt.Horizontal)
            action = QAction(str(col_name), self)
            action.setCheckable(True)
            action.setChecked(not self.isColumnHidden(col))
            action.triggered.connect(lambda checked, column=col: self.setColumnHidden(column, not checked))
            column_menu.addAction(action)
        
        # Export submenu
        export_menu = menu.addMenu("Export")
        export_csv_action = QAction("Export to CSV", self)
        export_csv_action.triggered.connect(lambda: self._export_data("csv"))
        export_menu.addAction(export_csv_action)
        
        export_excel_action = QAction("Export to Excel", self)
        export_excel_action.triggered.connect(lambda: self._export_data("excel"))
        export_menu.addAction(export_excel_action)
        
        menu.exec(self.mapToGlobal(position))
    
    def _copy_selection(self):
        """Copy selected cells to clipboard."""
        selection = self.selectedIndexes()
        if not selection:
            return
            
        # Sort by row, then column
        selection.sort(key=lambda idx: (idx.row(), idx.column()))
        
        # Group by rows
        rows = {}
        for index in selection:
            row = index.row()
            if row not in rows:
                rows[row] = []
            rows[row].append(index.data())
        
        # Format as tab-separated text
        text = "\n".join("\t".join(str(cell) for cell in row) for row in rows.values())
        
        # Copy to clipboard
        from PySide6.QtGui import QGuiApplication
        QGuiApplication.clipboard().setText(text)
    
    def _copy_row(self):
        """Copy entire row to clipboard."""
        selection = self.selectedIndexes()
        if not selection:
            return
            
        # Get the row of the first selected cell
        row = selection[0].row()
        
        # Get all cells in this row
        row_data = []
        for col in range(self.model().columnCount()):
            index = self.model().index(row, col)
            row_data.append(str(index.data()))
        
        # Format as tab-separated text
        text = "\t".join(row_data)
        
        # Copy to clipboard
        from PySide6.QtGui import QGuiApplication
        QGuiApplication.clipboard().setText(text)
    
    def _export_data(self, format_type):
        """Export data to file."""
        from PySide6.QtWidgets import QFileDialog
        
        # Get DataFrame from model
        df = self.get_dataframe()
        
        if format_type == "csv":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to CSV", "", "CSV Files (*.csv)")
            if file_path:
                df.to_csv(file_path, index=False)
        elif format_type == "excel":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to Excel", "", "Excel Files (*.xlsx)")
            if file_path:
                df.to_excel(file_path, index=False)
    
    def set_dataframe(self, df: pd.DataFrame):
        """Set the table data from a pandas DataFrame."""
        self._model.set_dataframe(df)
        
        # Auto-resize columns to contents initially
        self.resizeColumnsToContents()
        
        # Restore saved column widths if available
        header = self.horizontalHeader()
        for col, width in self._column_widths.items():
            if col < self._model.columnCount():
                header.resizeSection(col, width)
    
    def get_dataframe(self) -> pd.DataFrame:
        """Get the current data as a pandas DataFrame."""
        return self._model.get_dataframe()
    
    def set_editable_columns(self, columns: List[Union[int, str]]):
        """Set which columns should be editable."""
        # Convert column names to indices if needed
        col_indices = []
        for col in columns:
            if isinstance(col, str):
                for i in range(self._model.columnCount()):
                    if self._model.headerData(i, Qt.Horizontal) == col:
                        col_indices.append(i)
                        break
            else:
                col_indices.append(col)
        
        self._model.set_editable_columns(col_indices)
    
    def set_column_filter(self, column: Union[int, str], pattern: str):
        """Set filter for a specific column."""
        # Convert column name to index if needed
        col_idx = column
        if isinstance(column, str):
            for i in range(self._model.columnCount()):
                if self._model.headerData(i, Qt.Horizontal) == column:
                    col_idx = i
                    break
        
        self._proxy_model.set_column_filter(col_idx, pattern)
    
    def clear_filters(self):
        """Clear all filters."""
        self._proxy_model.clear_filters()
    
    def highlight_row(self, row: int, color: QColor = QColor(230, 230, 255)):
        """Highlight a specific row."""
        for col in range(self._model.columnCount()):
            item = self._model.item(row, col)
            if item:
                item.setBackground(color)
    
    def set_display_columns(self, columns, column_names=None):
        """Set which columns to display and their display names.
        
        Args:
            columns: List of database column names to display
            column_names: Dictionary mapping database column names to display names
        """
        # Store the database column names in the model
        self._model._display_columns = columns
        
        # Set the headers to display names
        headers = []
        for col in columns:
            display_name = col
            if column_names and col in column_names:
                display_name = column_names[col]
            headers.append(display_name)
        
        # Set the horizontal header labels
        self._model.setHorizontalHeaderLabels(headers)
        
        # Show only the specified columns
        for col_idx in range(self._model.columnCount()):
            # Get the original column name
            orig_col_name = self._model.headerData(col_idx, Qt.Horizontal, Qt.UserRole)
            if not orig_col_name:  # If not stored in UserRole, use the display name
                orig_col_name = self._model.headerData(col_idx, Qt.Horizontal)
            
            # Hide if not in the display columns
            self.setColumnHidden(col_idx, orig_col_name not in columns)

    def set_column_widths(self, width_map: Dict[str, int]):
        """Set custom column widths.
        
        Args:
            width_map: Dictionary mapping column names to widths (in characters)
        """
        if not width_map:
            return
        
        header = self.horizontalHeader()
        char_width = self.fontMetrics().averageCharWidth()
        
        for col_idx in range(self._model.columnCount()):
            # Get column name
            col_name = self._model.headerData(col_idx, Qt.Horizontal)
            
            # Apply width if in the width map
            if col_name in width_map:
                pixel_width = width_map[col_name] * char_width
                header.resizeSection(col_idx, pixel_width)


class EnhancedTableWidget(QWidget):
    """
    Complete table widget with filter controls and export options.
    Wraps EnhancedTableView with additional UI elements.
    """
    
    def __init__(self, parent=None):
        """Initialize the enhanced table widget."""
        super().__init__(parent)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Filter bar
        filter_frame = QFrame()
        filter_layout = QHBoxLayout(filter_frame)
        filter_layout.setContentsMargins(5, 5, 5, 5)
        
        # Filter label
        filter_label = QLabel("Filter:")
        filter_layout.addWidget(filter_label)
        
        # Column selector
        self.column_combo = QComboBox()
        filter_layout.addWidget(self.column_combo)
        
        # Filter input
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("Enter filter text...")
        filter_layout.addWidget(self.filter_input)
        
        # Apply filter button
        self.apply_filter_btn = QPushButton("Apply")
        filter_layout.addWidget(self.apply_filter_btn)
        
        # Clear filter button
        self.clear_filter_btn = QPushButton("Clear")
        filter_layout.addWidget(self.clear_filter_btn)
        
        # Column visibility button
        self.column_visibility_btn = QPushButton("Columns")
        filter_layout.addWidget(self.column_visibility_btn)
        
        # Export button
        self.export_btn = QPushButton("Export")
        filter_layout.addWidget(self.export_btn)
        
        # Add filter bar to main layout
        layout.addWidget(filter_frame)
        
        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # Table view
        self.table_view = EnhancedTableView()
        layout.addWidget(self.table_view, 1)  # Give table stretch priority
        
        # Connect signals
        self.apply_filter_btn.clicked.connect(self._apply_filter)
        self.clear_filter_btn.clicked.connect(self._clear_filter)
        self.column_visibility_btn.clicked.connect(self._show_column_visibility)
        self.export_btn.clicked.connect(self._show_export_menu)
        self.filter_input.returnPressed.connect(self._apply_filter)
    
    def _apply_filter(self):
        """Apply the current filter."""
        column = self.column_combo.currentData()
        pattern = self.filter_input.text()
        self.table_view.set_column_filter(column, pattern)
    
    def _clear_filter(self):
        """Clear all filters."""
        self.filter_input.clear()
        self.table_view.clear_filters()
    
    def _show_column_visibility(self):
        """Show column visibility menu."""
        menu = QMenu(self)
        
        for col in range(self.table_view._model.columnCount()):
            col_name = self.table_view._model.headerData(col, Qt.Horizontal)
            action = QAction(str(col_name), self)
            action.setCheckable(True)
            action.setChecked(not self.table_view.isColumnHidden(col))
            action.triggered.connect(lambda checked, column=col: 
                                    self.table_view.setColumnHidden(column, not checked))
            menu.addAction(action)
        
        menu.exec(self.column_visibility_btn.mapToGlobal(
            self.column_visibility_btn.rect().bottomLeft()))
    
    def _show_export_menu(self):
        """Show export options menu."""
        menu = QMenu(self)
        
        csv_action = QAction("Export to CSV", self)
        csv_action.triggered.connect(lambda: self.table_view._export_data("csv"))
        menu.addAction(csv_action)
        
        excel_action = QAction("Export to Excel", self)
        excel_action.triggered.connect(lambda: self.table_view._export_data("excel"))
        menu.addAction(excel_action)
        
        menu.exec(self.export_btn.mapToGlobal(
            self.export_btn.rect().bottomLeft()))
    
    def set_dataframe(self, df: pd.DataFrame):
        """Set the table data from a pandas DataFrame."""
        self.table_view.set_dataframe(df)
        
        # Update column combo box
        self.column_combo.clear()
        for col_idx, col_name in enumerate(df.columns):
            self.column_combo.addItem(str(col_name), col_idx)
    
    def get_dataframe(self) -> pd.DataFrame:
        """Get the current data as a pandas DataFrame."""
        return self.table_view.get_dataframe()
    
    def set_editable_columns(self, columns: List[Union[int, str]]):
        """Set which columns should be editable."""
        self.table_view.set_editable_columns(columns)

    def set_display_columns(self, columns, column_names=None):
        """Set which columns to display and their display names.
        
        Args:
            columns: List of database column names to display
            column_names: Dictionary mapping database column names to display names
        """
        # Store the display columns for reference
        self._display_columns = columns
        
        # Update the column combo box with display names
        self.column_combo.clear()
        for i, col in enumerate(columns):
            display_name = column_names.get(col, col) if column_names else col
            self.column_combo.addItem(display_name, i)
        
        # Set display columns in the table view
        self.table_view.set_display_columns(columns, column_names)

    def set_column_widths(self, width_map: Dict[str, int]):
        """Set custom column widths.
        
        Args:
            width_map: Dictionary mapping column names to widths (in characters)
        """
        self.table_view.set_column_widths(width_map)

