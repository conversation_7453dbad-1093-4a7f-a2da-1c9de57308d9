"""
Table View Toolbar v2 - Optimized Flatmate Pattern Implementation

Modular toolbar architecture implementing the optimized Flatmate pattern
with separate concerns for layout, state management, and extensibility.
"""

from typing import Dict, List, Optional, Any
from PySide6.QtCore import Signal, QObject
from PySide6.QtWidgets import QWidget, QHBoxLayout, QFrame

from .toolbar_manager import ToolbarManager
from .toolbar_factory import ToolbarFactory
from .groups import FilterGroup, ColumnGroup, ExportGroup


class TableViewToolbarV2(QFrame):
    """Complete toolbar implementing optimized Flatmate pattern with modular architecture."""
    
    # Signals for external communication
    filter_applied = Signal(object, str)  # column, pattern
    filters_cleared = Signal()
    column_visibility_requested = Signal()
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the table view toolbar with optimized Flatmate pattern."""
        super().__init__(parent)
        self.setObjectName("TableViewToolbarV2")
        
        # Initialize core components
        self._toolbar_manager = ToolbarManager()
        self._toolbar_factory = ToolbarFactory()
        
        # Initialize UI
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components with optimized layout."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(12)
        
        # Create toolbar groups using factory
        self.filter_group = self._toolbar_factory.create_filter_group()
        self.column_group = self._toolbar_factory.create_column_group()
        self.export_group = self._toolbar_factory.create_export_group()
        
        # Add groups to layout
        layout.addWidget(self.filter_group)
        layout.addStretch()
        layout.addWidget(self.column_group)
        layout.addWidget(self.export_group)
    
    def _connect_signals(self):
        """Connect all signals for the optimized architecture."""
        # Connect group signals to toolbar signals
        self.filter_group.filter_applied.connect(self.filter_applied)
        self.filter_group.filters_cleared.connect(self.filters_cleared)
        self.column_group.column_visibility_requested.connect(self.column_visibility_requested)
        self.export_group.csv_export_requested.connect(self.csv_export_requested)
        self.export_group.excel_export_requested.connect(self.excel_export_requested)
    
    def set_columns(self, columns: List[str], column_names: Optional[Dict[str, str]] = None):
        """Set available columns for the toolbar."""
        self.filter_group.set_columns(columns, column_names)
        self.column_group.set_columns(columns, column_names)
    
    def get_state(self) -> Dict[str, Any]:
        """Get current toolbar state for persistence."""
        return {
            'filter_state': self.filter_group.get_state(),
            'column_state': self.column_group.get_state(),
            'export_state': self.export_group.get_state()
        }
    
    def set_state(self, state: Dict[str, Any]):
        """Restore toolbar state from persistence."""
        self.filter_group.set_state(state.get('filter_state', {}))
        self.column_group.set_state(state.get('column_state', {}))
        self.export_group.set_state(state.get('export_state', {}))