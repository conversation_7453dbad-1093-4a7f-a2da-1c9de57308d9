# Toolbar GUI Refinements - Technical Design

*Document Version: 1.0.0*  
*Created: 2025-07-18*  
*Status: Design Phase*

## 1. Current Architecture

### 1.1 Relevant Files and Components

#### Core Files
- `flatmate/src/fm/modules/categorize/_view/cat_view.py` - Main view for the Categorize module
- `flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py` - Transaction table panel with filtering capabilities
- `flatmate/src/fm/modules/categorize/_view/components/left_panel/left_panel.py` - Left panel with database filtering options
- `flatmate/src/fm/modules/categorize/cat_presenter.py` - Presenter handling UI and data interactions

#### Supporting Files
- `flatmate/src/fm/gui/_shared_components/table_view/fm_table_view.py` - Custom table view component
- `flatmate/src/fm/modules/categorize/config/config.py` - Configuration management for the Categorize module

### 1.2 Current Implementation

The current filtering system follows a two-tier architecture:

1. **Database Query Filtering (Left Panel)**
   - Applied to database queries, triggering table reload
   - Includes date range selection and account filtering
   - Implemented in `LeftPanelWidget` class with components:
     - `DateFilterPane` for date range selection
     - `AccountSelector` for account filtering

2. **Table View Filtering (Top Toolbar)**
   - Applied to already-loaded data without database reload
   - Currently has limited implementation for real-time search
   - Lacks consistent UI elements for filtering by category, tags, etc.

## 2. Required Changes

### 2.1 Component Modifications

#### Transaction View Panel Enhancements
- Enhance the toolbar in `transaction_view_panel.py` with consistent UI elements
- Implement proper layout management for toolbar components
- Add icon support for toolbar buttons

#### Icon Integration
- Use SVG icons from Google Material icons as specified in `_discussion.md`
- Create consistent icon sizing and styling

#### Toolbar Components
- Text entry field with search icon and clear button
- Apply button with consistent styling
- Columns dropdown with improved visibility

### 2.2 New Components

#### SearchBar Component
```python
class SearchBar(QWidget):
    """Enhanced search bar with clear button and search icon."""
    
    search_changed = Signal(str)
    search_cleared = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
```

#### ColumnSelector Component
```python
class ColumnSelector(QWidget):
    """Column visibility selector with dropdown menu."""
    
    columns_changed = Signal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
```

#### ToolbarButton Component
```python
class ToolbarButton(QPushButton):
    """Consistent styled button for the toolbar."""
    
    def __init__(self, text="", icon_path=None, parent=None):
        super().__init__(text, parent)
        self._init_ui(icon_path)
```

## 3. Integration Points

### 3.1 Presenter Integration

The `CategorizePresenter` class will need to handle:
- Live filtering signals from the toolbar components
- Column visibility changes
- Apply button actions

```python
def _connect_view_signals(self):
    """Connect signals from the view to presenter methods."""
    # Existing connections...
    
    # New connections for toolbar
    self.view.center_panel.transaction_view.search_changed.connect(self._handle_search_filter)
    self.view.center_panel.transaction_view.columns_changed.connect(self._handle_columns_changed)
```

### 3.2 Configuration Integration

The toolbar state should be persisted in the configuration:
- Search text (optional)
- Visible columns
- Filter state

```python
# Default configuration values
config.ensure_defaults({
    'categorize.toolbar.remember_search': False,
    'categorize.toolbar.last_search': '',
    'categorize.toolbar.visible_columns': ['date', 'details', 'amount', 'account', 'tags'],
})
```

## 4. UI/UX Considerations

### 4.1 Layout and Spacing

- Toolbar should have consistent padding (8px vertical, 12px horizontal)
- Components should be properly aligned and spaced
- Search field should expand to fill available space
- Buttons should have consistent size and appearance

### 4.2 Responsiveness

- Toolbar should adapt to different window sizes
- Components should collapse gracefully on narrow windows
- Search field should maintain minimum usable width

### 4.3 Visual Feedback

- Search field should highlight when active
- Clear button should only appear when text is present
- Apply button should have hover and pressed states
- Column selector should indicate when non-default columns are selected

## 5. Performance Considerations

- Live filtering should use debouncing (300ms) to prevent excessive updates
- Column visibility changes should be efficient
- Icon loading should be optimized (SVG preferred over PNG)

## 6. Testing Approach

- Unit tests for individual toolbar components
- Integration tests for toolbar with transaction view
- Visual tests for layout and responsiveness
- Performance tests for filtering large datasets

## 7. Dependencies

- PySide6 Qt components
- SVG icons from Google Material Design
- Existing configuration system
- Custom table view component

## 8. Technical Risks and Mitigations

| Risk | Impact | Mitigation |
|------|--------|------------|
| Performance degradation with large datasets | High | Implement debouncing for live filtering |
| Inconsistent UI across platforms | Medium | Use Qt style sheets for consistent appearance |
| Icon loading issues | Low | Fallback to text labels if icons fail to load |
| Column visibility persistence issues | Medium | Add robust error handling for config loading/saving |
