# GUI Component Quick Reference

Fast reference for creating GUI components following the established protocol.

---

## Decision Tree: What Am I Creating?

```
Is it a foundation class that others inherit from?
├─ YES → Base Class (goes in base/)
│   ├─ Widget foundation? → base/widgets/
│   ├─ Toolbar foundation? → base/toolbars/
│   ├─ Panel foundation? → base/panels/
│   └─ Layout management? → base/layouts/
│
└─ NO → Is it a complete, reusable component?
    ├─ YES → Reusable Component (gets own folder)
    │   └─ Create: component_name/ folder
    │
    └─ NO → Is it a helper function or utility?
        └─ YES → Utility (goes in utils/)
```

---

## Quick Start Checklist

### Creating a Base Class
- [ ] **Location**: `gui/_shared_components/base/[category]/`
- [ ] **Name**: `Base[ComponentType]` (e.g., `BaseWidget`, `BaseToolbar`)
- [ ] **Inherit**: From appropriate Qt class + mixins
- [ ] **Provide**: Common functionality, signals, styling hooks
- [ ] **Avoid**: Business logic - keep it foundational

### Creating a Reusable Component
- [ ] **Location**: `gui/_shared_components/[component_name]/`
- [ ] **Structure**: Main file + config + components/ + utils/
- [ ] **Name**: `PascalCase` class, `snake_case` files/folders
- [ ] **Inherit**: From appropriate base class
- [ ] **Include**: Configuration, styling, tests

### Creating a Utility
- [ ] **Location**: `gui/_shared_components/utils/`
- [ ] **Purpose**: Helper functions, not classes
- [ ] **Stateless**: No instance variables
- [ ] **Focused**: Single responsibility

---

## File Templates

### Base Class Template
```python
# base/widgets/base_[type].py
from PySide6.QtWidgets import Q[BaseWidget]
from PySide6.QtCore import Signal
from ..mixins.configurable_mixin import ConfigurableMixin

class Base[Type](Q[BaseWidget], ConfigurableMixin):
    """Base class for [type] widgets."""
    
    # Standard signals
    [common_signal] = Signal([type])
    
    def __init__(self, parent=None):
        super().__init__(parent)
        ConfigurableMixin.__init__(self)
        self._init_base_[type]()
    
    def _init_base_[type](self):
        """Initialize base functionality."""
        self.setObjectName("Base[Type]")
        self._apply_base_styling()
    
    def _apply_base_styling(self):
        """Apply base styling - override in subclasses."""
        pass
```

### Component Template
```python
# [component_name]/[component_name].py
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Signal
from ..base.widgets.base_widget import BaseWidget
from .config import [ComponentName]Config

class [ComponentName](BaseWidget):
    """[Component description]."""
    
    # Component signals
    [signal_name] = Signal([type])
    
    def __init__(self, config: [ComponentName]Config = None, parent=None):
        super().__init__(parent)
        self.config = config or [ComponentName]Config()
        self.config.validate()
        
        self._init_ui()
        self._connect_signals()
        self._apply_styling()
    
    def _init_ui(self):
        """Initialize component UI."""
        pass
    
    def _connect_signals(self):
        """Connect component signals."""
        pass
    
    def _apply_styling(self):
        """Apply component styling."""
        pass
```

### Config Template
```python
# [component_name]/config.py
from fm.core.config import BaseConfig

class [ComponentName]Config(BaseConfig):
    """Configuration for [ComponentName]."""
    
    def __init__(self):
        super().__init__()
        self.[setting] = [default_value]
    
    def validate(self):
        """Validate configuration."""
        if self.[setting] < 0:
            raise ValueError("[setting] must be non-negative")
```

---

## Common Patterns

### Signal Naming
- **Past Tense**: `item_selected`, `data_loaded`, `search_completed`
- **Descriptive**: `filter_applied`, `column_visibility_changed`
- **Consistent**: Use same patterns across similar components

### Method Naming
- **Private Init**: `_init_ui()`, `_init_[component]()`
- **Private Handlers**: `_on_[event]()`, `_handle_[action]()`
- **Public API**: `set_[property]()`, `get_[property]()`, `update_[data]()`

### Configuration Patterns
```python
# Always provide defaults
self.setting = config.setting if config else default_value

# Always validate
def validate(self):
    if self.value < 0:
        raise ValueError("value must be non-negative")

# Use type hints
def __init__(self, setting: int = 100):
```

---

## Import Patterns

### Base Class Imports
```python
# In base classes
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Signal, QObject
from ..mixins.configurable_mixin import ConfigurableMixin

# In components using base classes
from ..base.widgets.base_widget import BaseWidget
from ..base.toolbars.base_toolbar import BaseToolbar
```

### Component Imports
```python
# In __init__.py files
from .component_name import ComponentName
from .config import ComponentNameConfig

__all__ = ['ComponentName', 'ComponentNameConfig']

# In other modules
from gui._shared_components.component_name import ComponentName
```

---

## Testing Patterns

### Basic Test Structure
```python
# tests/test_[component].py
import pytest
from PySide6.QtWidgets import QApplication
from ..[component] import [ComponentName]

class Test[ComponentName]:
    def test_initialization(self):
        component = [ComponentName]()
        assert component is not None
    
    def test_signals(self):
        component = [ComponentName]()
        signal_received = False
        
        def handler():
            nonlocal signal_received
            signal_received = True
        
        component.[signal].connect(handler)
        component.[trigger_method]()
        assert signal_received
```

---

## Common Mistakes to Avoid

### ❌ Wrong Patterns
```python
# Don't put business logic in base classes
class BaseWidget:
    def calculate_tax(self):  # ❌ Business logic
        pass

# Don't hardcode styling
class MyWidget:
    def __init__(self):
        self.setStyleSheet("color: red;")  # ❌ Hardcoded

# Don't skip configuration
class MyComponent:
    def __init__(self):
        self.max_items = 100  # ❌ No configuration
```

### ✅ Correct Patterns
```python
# Base classes provide infrastructure
class BaseWidget:
    def apply_theme_styles(self, style_type):  # ✅ Infrastructure
        pass

# Use theme system
class MyWidget:
    def _apply_styling(self):
        self.apply_theme_styles("widget")  # ✅ Theme-aware

# Always use configuration
class MyComponent:
    def __init__(self, config=None):
        self.config = config or MyComponentConfig()  # ✅ Configurable
```

---

## Migration Checklist

When updating existing components to follow protocol:

- [ ] **Move to correct location** based on classification
- [ ] **Rename files/classes** to follow conventions
- [ ] **Add configuration class** if missing
- [ ] **Update imports** throughout codebase
- [ ] **Add proper base class inheritance**
- [ ] **Implement standard signals** and methods
- [ ] **Add styling support** via theme system
- [ ] **Write/update tests** to cover functionality
- [ ] **Update documentation** and examples
- [ ] **Test integration** with existing code

---

## Quick Commands

### Create New Component Structure
```bash
# Create component folder
mkdir gui/_shared_components/my_component

# Create standard files
touch gui/_shared_components/my_component/__init__.py
touch gui/_shared_components/my_component/my_component.py
touch gui/_shared_components/my_component/config.py
mkdir gui/_shared_components/my_component/components
mkdir gui/_shared_components/my_component/tests
```

### Check Protocol Compliance
```bash
# Find components that might need migration
find gui/_shared_components -name "*.py" -type f | grep -v __pycache__ | grep -v test
```

---

**Use this reference for quick decisions and consistent component creation.**
