"""
Test Script for Simple Table View Toolbar

Test the working simple implementation.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parents[7]
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import <PERSON>Application, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

from table_view_toolbar_simple import TableViewToolbarSimple


class SimpleToolbarTestWindow(QMainWindow):
    """Test window for the simple toolbar."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Simple Table View Toolbar Test")
        self.setGeometry(100, 100, 800, 200)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Add title
        title = QLabel("Simple Table View Toolbar Test")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: white;")
        layout.addWidget(title)
        
        # Create and add the toolbar
        self.toolbar = TableViewToolbarSimple()
        layout.addWidget(self.toolbar)
        
        # Add some test columns (including internal ones that should be filtered)
        test_columns = [
            'id', 'date', 'details', 'amount', 'balance', 'account',
            'db_uid', 'source_uid', 'is_deleted'  # These should be filtered out
        ]
        
        test_column_names = {
            'id': 'ID',
            'date': 'Date',
            'details': 'Details', 
            'amount': 'Amount',
            'balance': 'Balance',
            'account': 'Account',
            'db_uid': 'Database UID',
            'source_uid': 'Source UID',
            'is_deleted': 'Is Deleted'
        }
        
        self.toolbar.set_columns(test_columns, test_column_names)
        
        # Connect signals for testing
        self._connect_test_signals()
        
        # Add status label
        self.status_label = QLabel("Ready - Try searching or clicking buttons")
        self.status_label.setStyleSheet("margin: 10px; padding: 5px; background-color: #2D2D2D; border-radius: 3px; color: white;")
        layout.addWidget(self.status_label)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        # Apply dark theme styling
        self._apply_dark_theme()
    
    def _connect_test_signals(self):
        """Connect toolbar signals for testing."""
        self.toolbar.filter_applied.connect(self._on_filter_applied)
        self.toolbar.filters_cleared.connect(self._on_filters_cleared)
        self.toolbar.column_visibility_requested.connect(self._on_column_visibility_requested)
        self.toolbar.csv_export_requested.connect(self._on_csv_export_requested)
    
    def _on_filter_applied(self, column, pattern):
        """Handle filter applied signal."""
        self.status_label.setText(f"Filter Applied: '{pattern}' in column '{column}'")
        print(f"DEBUG: Filter applied - Column: {column}, Pattern: {pattern}")
    
    def _on_filters_cleared(self):
        """Handle filters cleared signal."""
        self.status_label.setText("Filters Cleared")
        print("DEBUG: Filters cleared")
    
    def _on_column_visibility_requested(self):
        """Handle column visibility request."""
        self.status_label.setText("Column Visibility Requested")
        print("DEBUG: Column visibility requested")
    
    def _on_csv_export_requested(self):
        """Handle CSV export request."""
        self.status_label.setText("CSV Export Requested")
        print("DEBUG: CSV export requested")
    
    def _apply_dark_theme(self):
        """Apply a simple dark theme for testing."""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1E1E1E;
                color: #FFFFFF;
            }
            
            QWidget {
                background-color: #1E1E1E;
            }
            
            QLabel {
                color: #FFFFFF;
            }
        """)


def main():
    """Run the simple toolbar test."""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = SimpleToolbarTestWindow()
    window.show()
    
    print("Simple Toolbar Test Started")
    print("=" * 50)
    print("Expected layout: [Eye/👁] [Search Box] 'in:' [Dropdown] [Export/📤]")
    print("Expected behavior:")
    print("1. Internal columns (db_uid, source_uid, is_deleted) should NOT appear in dropdown")
    print("2. Search box should be functional with live filtering")
    print("3. Buttons should be clickable and emit signals")
    print("4. Column names should be formatted nicely (Details, Amount, etc.)")
    print("=" * 50)
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
