"""
Comprehensive test suite for package-based search implementation.

Tests the integration of luqum package with the enhanced filter proxy model,
ensuring backward compatibility and new advanced features work correctly.
"""

import sys
import os

# Add the project root to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../../../../..'))

try:
    from search_query_parser import SearchQueryParser, SearchQueryPreprocessor
    from enhanced_filter_proxy_model import EnhancedFilterProxyModel
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)


class TestPackageBasedSearch:
    """Test suite for package-based search functionality."""
    
    def __init__(self):
        self.parser = SearchQueryParser()
        self.preprocessor = SearchQueryPreprocessor()
        self.proxy_model = EnhancedFilterProxyModel()
        self.test_results = []
    
    def run_all_tests(self):
        """Run all test suites."""
        print("Package-Based Search Test Suite")
        print("=" * 50)
        
        # Test individual components
        self.test_preprocessor()
        self.test_parser_availability()
        self.test_basic_parsing()
        self.test_advanced_parsing()
        self.test_evaluation()
        self.test_backward_compatibility()
        self.test_integration()
        self.test_performance()
        
        # Print summary
        self.print_summary()
    
    def test_preprocessor(self):
        """Test the query preprocessor."""
        print("\n1. Testing SearchQueryPreprocessor...")
        
        test_cases = [
            # (input, expected_output, description)
            ("coffee tea", "coffee tea", "Basic AND (implicit)"),
            ("coffee|tea", "coffee OR tea", "OR with pipe"),
            ("coffee/tea", "coffee OR tea", "OR with slash"),
            ("-decaf", "NOT decaf", "NOT with dash"),
            ("(coffee|tea) -decaf", "(coffee OR tea) NOT decaf", "Complex expression"),
            ('"coffee shop"', '"coffee shop"', "Quoted phrase"),
            ("coffee|tea hot", "coffee OR tea hot", "Mixed OR and AND"),
            ("", "", "Empty query"),
            ("  coffee   tea  ", "coffee tea", "Whitespace normalization"),
        ]
        
        for input_query, expected, description in test_cases:
            result = self.preprocessor.preprocess(input_query)
            success = result == expected
            self.test_results.append(("Preprocessor", description, success))
            
            status = "✓" if success else "✗"
            print(f"  {status} {description}: '{input_query}' → '{result}'")
            if not success:
                print(f"    Expected: '{expected}'")
    
    def test_parser_availability(self):
        """Test parser availability and initialization."""
        print("\n2. Testing Parser Availability...")
        
        available = self.parser.is_available()
        self.test_results.append(("Parser", "luqum availability", available))
        
        status = "✓" if available else "✗"
        print(f"  {status} luqum package available: {available}")
        
        if available:
            print(f"  ✓ SearchQueryParser initialized successfully")
        else:
            print(f"  ⚠ luqum not available, fallback mode will be used")
    
    def test_basic_parsing(self):
        """Test basic parsing functionality."""
        print("\n3. Testing Basic Parsing...")
        
        if not self.parser.is_available():
            print("  ⚠ Skipping parsing tests - luqum not available")
            return
        
        test_cases = [
            "coffee",
            "coffee tea",
            "coffee OR tea",
            "coffee AND tea",
            "NOT decaf",
            '"coffee shop"',
        ]
        
        for query in test_cases:
            try:
                ast = self.parser.parse(query)
                success = ast is not None
                self.test_results.append(("Parser", f"Parse '{query}'", success))
                
                status = "✓" if success else "✗"
                print(f"  {status} Parsed '{query}' → {type(ast).__name__}")
            except Exception as e:
                self.test_results.append(("Parser", f"Parse '{query}'", False))
                print(f"  ✗ Failed to parse '{query}': {e}")
    
    def test_advanced_parsing(self):
        """Test advanced parsing features."""
        print("\n4. Testing Advanced Parsing...")
        
        if not self.parser.is_available():
            print("  ⚠ Skipping advanced parsing tests - luqum not available")
            return
        
        test_cases = [
            "(coffee OR tea) AND NOT decaf",
            "coffee|tea -decaf",  # User-friendly syntax
            '"exact phrase" OR single',
            "((coffee OR tea) AND hot) NOT decaf",
        ]
        
        for query in test_cases:
            try:
                ast = self.parser.parse(query)
                success = ast is not None
                self.test_results.append(("Advanced Parser", f"Parse '{query}'", success))
                
                status = "✓" if success else "✗"
                print(f"  {status} Parsed complex '{query}' → {type(ast).__name__}")
            except Exception as e:
                self.test_results.append(("Advanced Parser", f"Parse '{query}'", False))
                print(f"  ✗ Failed to parse complex '{query}': {e}")
    
    def test_evaluation(self):
        """Test query evaluation against sample data."""
        print("\n5. Testing Query Evaluation...")
        
        test_data = [
            "Starbucks coffee shop purchase",
            "Tea house visit",
            "Decaf coffee from cafe",
            "Regular coffee and tea",
            "Gas station snacks",
        ]
        
        test_queries = [
            ("coffee", [0, 2, 3], "Simple word match"),
            ("coffee OR tea", [0, 1, 2, 3], "OR operation"),
            ("coffee -decaf", [0, 3], "AND with NOT"),
            ('"coffee shop"', [0], "Exact phrase match"),
            ("gas", [4], "Single word match"),
            ("nonexistent", [], "No matches"),
        ]
        
        for query, expected_indices, description in test_queries:
            matches = []
            for i, data in enumerate(test_data):
                if self.parser.evaluate(query, data):
                    matches.append(i)
            
            success = matches == expected_indices
            self.test_results.append(("Evaluation", description, success))
            
            status = "✓" if success else "✗"
            print(f"  {status} {description}: '{query}' → {len(matches)} matches")
            
            if not success:
                print(f"    Expected indices: {expected_indices}")
                print(f"    Actual indices: {matches}")
    
    def test_backward_compatibility(self):
        """Test backward compatibility with Phase 1 syntax."""
        print("\n6. Testing Backward Compatibility...")
        
        # Phase 1 syntax patterns that must continue to work
        test_cases = [
            ("coffee shop", "Starbucks coffee shop", True, "AND logic"),
            ("coffee shop", "Tea house", False, "AND logic - no match"),
            ("-decaf", "Regular coffee", True, "Exclude logic - not present"),
            ("-decaf", "Decaf coffee", False, "Exclude logic - present"),
            ("coffee -decaf", "Regular coffee", True, "Combined AND/exclude"),
            ("coffee -decaf", "Decaf coffee", False, "Combined AND/exclude - excluded"),
            ("", "Any text", True, "Empty query matches all"),
        ]
        
        for query, data, expected, description in test_cases:
            result = self.parser.evaluate(query, data)
            success = result == expected
            self.test_results.append(("Backward Compatibility", description, success))
            
            status = "✓" if success else "✗"
            print(f"  {status} {description}: '{query}' vs '{data}' → {result}")
    
    def test_integration(self):
        """Test integration with EnhancedFilterProxyModel."""
        print("\n7. Testing Integration with Filter Proxy Model...")
        
        test_cases = [
            ("coffee", "Starbucks coffee shop", True, "Basic integration"),
            ("coffee OR tea", "Tea house visit", True, "OR operation integration"),
            ("coffee -decaf", "Decaf coffee", False, "NOT operation integration"),
            ('"coffee shop"', "Starbucks coffee shop", True, "Phrase integration"),
        ]
        
        for pattern, data, expected, description in test_cases:
            try:
                result = self.proxy_model._check_pattern_match(data, pattern)
                success = result == expected
                self.test_results.append(("Integration", description, success))
                
                status = "✓" if success else "✗"
                print(f"  {status} {description}: '{pattern}' vs '{data}' → {result}")
            except Exception as e:
                self.test_results.append(("Integration", description, False))
                print(f"  ✗ {description}: Error - {e}")
    
    def test_performance(self):
        """Test performance characteristics."""
        print("\n8. Testing Performance...")
        
        import time
        
        # Test parsing performance
        query = "(coffee OR tea) AND NOT decaf"
        iterations = 100
        
        start_time = time.time()
        for _ in range(iterations):
            try:
                self.parser.parse(query)
            except:
                pass
        parse_time = (time.time() - start_time) * 1000 / iterations
        
        # Test evaluation performance
        data = "Starbucks coffee shop with tea and regular coffee"
        start_time = time.time()
        for _ in range(iterations):
            try:
                self.parser.evaluate(query, data)
            except:
                pass
        eval_time = (time.time() - start_time) * 1000 / iterations
        
        parse_success = parse_time < 10  # Should be under 10ms per parse
        eval_success = eval_time < 5    # Should be under 5ms per evaluation
        
        self.test_results.append(("Performance", "Parse time", parse_success))
        self.test_results.append(("Performance", "Evaluation time", eval_success))
        
        print(f"  Average parse time: {parse_time:.2f}ms {'✓' if parse_success else '✗'}")
        print(f"  Average evaluation time: {eval_time:.2f}ms {'✓' if eval_success else '✗'}")
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("TEST SUMMARY")
        print("=" * 50)
        
        # Group results by category
        categories = {}
        for category, test_name, success in self.test_results:
            if category not in categories:
                categories[category] = []
            categories[category].append((test_name, success))
        
        total_tests = len(self.test_results)
        total_passed = sum(1 for _, _, success in self.test_results if success)
        
        for category, tests in categories.items():
            passed = sum(1 for _, success in tests if success)
            total = len(tests)
            print(f"\n{category}: {passed}/{total} passed")
            
            for test_name, success in tests:
                status = "✓" if success else "✗"
                print(f"  {status} {test_name}")
        
        print(f"\nOVERALL: {total_passed}/{total_tests} tests passed")
        
        if total_passed == total_tests:
            print("🎉 All tests passed! Package-based search is working correctly.")
        else:
            print(f"⚠ {total_tests - total_passed} tests failed. Review implementation.")


def main():
    """Run the test suite."""
    test_suite = TestPackageBasedSearch()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()
