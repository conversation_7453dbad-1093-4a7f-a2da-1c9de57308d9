#!/usr/bin/env python3
"""
Test script for the filter persistence and AND/exclude functionality.
"""

import sys
import os
import pandas as pd
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

from fm.gui._shared_components.table_view_v2.fm_table_view import CustomTableView_v2
from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel


class TestWindow(QMainWindow):
    """Test window for filter persistence and AND/exclude functionality."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Filter Persistence & AND/Exclude Test")
        self.setGeometry(100, 100, 1000, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create test data
        self.test_data = pd.DataFrame({
            'Date': ['2025-01-01', '2025-01-02', '2025-01-03', '2025-01-04', '2025-01-05', '2025-01-06'],
            'Details': ['Coffee Shop Purchase', 'Grocery Store', 'Gas Station', 'Coffee Shop Refund', 'Restaurant Bill', 'Online Shopping'],
            'Amount': [4.50, -85.20, -45.00, 4.50, -32.75, -125.99],
            'Account': ['Checking', 'Checking', 'Credit Card', 'Checking', 'Credit Card', 'Savings'],
            'Category': ['Food', 'Groceries', 'Transport', 'Food', 'Food', 'Shopping']
        })
        
        # Create table view with persistence enabled
        self.table_view = CustomTableView_v2()
        self.table_view.configure(
            auto_size_columns=True,
            max_column_width=40,
            show_toolbar=True,
            save_filter_state=True,  # Enable persistence
            default_filter_column="Details"
        )
        
        # Set the test data
        self.table_view.set_dataframe(self.test_data)
        layout.addWidget(self.table_view)
        
        # Add test controls
        controls_layout = QVBoxLayout()
        
        # Test buttons
        test_and_button = QPushButton("Test AND Logic: 'Coffee Shop'")
        test_and_button.clicked.connect(lambda: self.test_filter("Coffee Shop"))
        controls_layout.addWidget(test_and_button)
        
        test_exclude_button = QPushButton("Test EXCLUDE Logic: 'Coffee -Refund'")
        test_exclude_button.clicked.connect(lambda: self.test_filter("Coffee -Refund"))
        controls_layout.addWidget(test_exclude_button)
        
        test_complex_button = QPushButton("Test Complex: 'Shop -Coffee'")
        test_complex_button.clicked.connect(lambda: self.test_filter("Shop -Coffee"))
        controls_layout.addWidget(test_complex_button)
        
        clear_button = QPushButton("Clear Filter")
        clear_button.clicked.connect(self.clear_filter)
        controls_layout.addWidget(clear_button)
        
        # Test persistence
        persistence_button = QPushButton("Test Persistence (Set 'Food' filter)")
        persistence_button.clicked.connect(lambda: self.test_persistence("Food"))
        controls_layout.addWidget(persistence_button)
        
        # Status label
        self.status_label = QLabel("Ready for testing")
        controls_layout.addWidget(self.status_label)
        
        layout.addLayout(controls_layout)
        
    def test_filter(self, pattern):
        """Test a filter pattern."""
        try:
            # Apply filter through the UI
            if hasattr(self.table_view.toolbar, 'filter_group'):
                filter_group = self.table_view.toolbar.filter_group
                filter_group.filter_input.setText(pattern)
                # The live filtering should apply automatically
                
                # Get visible row count
                proxy_model = self.table_view.table_view.model()
                visible_rows = proxy_model.rowCount()
                
                self.status_label.setText(f"Filter '{pattern}' applied. Visible rows: {visible_rows}")
                
                # Test the pattern parsing directly
                if hasattr(proxy_model, '_parse_filter_pattern'):
                    and_terms, exclude_terms = proxy_model._parse_filter_pattern(pattern)
                    print(f"Pattern '{pattern}' parsed as:")
                    print(f"  AND terms: {and_terms}")
                    print(f"  EXCLUDE terms: {exclude_terms}")
                
            else:
                self.status_label.setText("Error: Filter group not found")
                
        except Exception as e:
            self.status_label.setText(f"Error testing filter: {e}")
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()
    
    def clear_filter(self):
        """Clear the current filter."""
        try:
            if hasattr(self.table_view.toolbar, 'filter_group'):
                filter_group = self.table_view.toolbar.filter_group
                filter_group.filter_input.clear()
                self.status_label.setText("Filter cleared")
            else:
                self.status_label.setText("Error: Filter group not found")
        except Exception as e:
            self.status_label.setText(f"Error clearing filter: {e}")
    
    def test_persistence(self, pattern):
        """Test filter persistence."""
        try:
            # Set a filter
            if hasattr(self.table_view.toolbar, 'filter_group'):
                filter_group = self.table_view.toolbar.filter_group
                filter_group.filter_input.setText(pattern)
                
                # Check if persistence saved the state
                config = self.table_view._config
                saved_pattern = config.last_filter_pattern
                saved_column = config.last_filter_column
                
                self.status_label.setText(f"Persistence test: Pattern='{saved_pattern}', Column='{saved_column}'")
                print(f"Filter persistence state:")
                print(f"  Saved pattern: {saved_pattern}")
                print(f"  Saved column: {saved_column}")
                print(f"  Save enabled: {config.save_filter_state}")
                
            else:
                self.status_label.setText("Error: Filter group not found")
                
        except Exception as e:
            self.status_label.setText(f"Error testing persistence: {e}")
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()


def test_pattern_parsing():
    """Test the pattern parsing logic directly."""
    print("=== Testing Pattern Parsing Logic ===")
    
    # Create a proxy model instance for testing
    proxy = EnhancedFilterProxyModel()
    
    test_cases = [
        ("foo bar", (["foo", "bar"], [])),
        ("foo -bar", (["foo"], ["bar"])),
        ("-exclude", ([], ["exclude"])),
        ("test -", (["test", "-"], [])),
        ("", ([], [])),
        ("Coffee -Refund", (["coffee"], ["refund"])),
        ("Shop -Coffee", (["shop"], ["coffee"]))
    ]
    
    for pattern, expected in test_cases:
        try:
            result = proxy._parse_filter_pattern(pattern)
            status = "✅ PASS" if result == expected else "❌ FAIL"
            print(f"{status} Pattern: '{pattern}' → {result} (expected {expected})")
        except Exception as e:
            print(f"❌ ERROR Pattern: '{pattern}' → {e}")


def main():
    """Main test function."""
    app = QApplication(sys.argv)
    
    # Test pattern parsing first
    test_pattern_parsing()
    
    # Create and show test window
    window = TestWindow()
    window.show()
    
    print("\n=== Filter Persistence & AND/Exclude Test ===")
    print("1. Test AND logic: Both terms must be present")
    print("2. Test EXCLUDE logic: Terms with - prefix are excluded")
    print("3. Test persistence: Filters should be saved in config")
    print("4. Check placeholder text shows new syntax")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
