"""
Base Toolbar Classes

Provides foundation classes for creating consistent, reusable toolbars
throughout the application using Qt's QToolBar and custom QFrame approaches.
"""

from typing import Dict, List, Optional, Any
from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import <PERSON><PERSON><PERSON>Bar, QFrame, QWidget, QHBoxLayout, QLabel, QComboBox
from PySide6.QtGui import QAction

from .layout_manager import Tool<PERSON><PERSON>ayoutManager, TableViewToolbarLayout
from .base_toolbar_button import BaseToolbarButton


class BaseQToolBar(QToolBar):
    """Base class using Qt's QToolBar for maximum compatibility and features.
    
    Provides:
    - Standard QToolBar functionality (movable, dockable, etc.)
    - Consistent styling via QSS
    - Icon management integration
    - Layout management
    """
    
    def __init__(self, title: str = "", parent=None):
        """Initialize base QToolBar.
        
        Args:
            title: Toolbar title
            parent: Parent widget
        """
        super().__init__(title, parent)
        self.setObjectName("BaseQToolBar")
        
        # Configure toolbar
        self.setMovable(False)  # Can be overridden by subclasses
        self.setFloatable(False)
        self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonIconOnly)
        self.setIconSize(self.iconSize())  # Use default or override
        
        # Initialize layout manager
        self.layout_manager = ToolbarLayoutManager()
        self.layout_manager.setup_qtoolbar_layout(self)
        
        self._setup_styling()
    
    def _setup_styling(self):
        """Apply base QSS styling."""
        self.setStyleSheet("""
            QToolBar#BaseQToolBar {
                background-color: var(--color-bg-dark, #1E1E1E);
                border: 1px solid var(--color-border, #333333);
                border-radius: 4px;
                spacing: 4px;
                padding: 4px;
            }
            
            QToolBar#BaseQToolBar::separator {
                background: var(--color-border, #333333);
                width: 1px;
                height: 20px;
                margin: 0 2px;
            }
        """)
    
    def add_button(self, icon_name: str, tooltip: str, 
                   style_variant: str = "default") -> BaseToolbarButton:
        """Add a standardized button to the toolbar.
        
        Args:
            icon_name: Icon name for the button
            tooltip: Tooltip text
            style_variant: Style variant for the button
            
        Returns:
            Created BaseToolbarButton
        """
        button = BaseToolbarButton(icon_name, tooltip, style_variant)
        self.addWidget(button)
        return button
    
    def add_separator_line(self):
        """Add a visual separator line."""
        self.addSeparator()
    
    def add_stretch(self):
        """Add stretch space to push subsequent widgets right."""
        # QToolBar doesn't have addStretch, so we add an expanding widget
        stretch_widget = QWidget()
        stretch_widget.setSizePolicy(stretch_widget.sizePolicy().Expanding, 
                                   stretch_widget.sizePolicy().Preferred)
        self.addWidget(stretch_widget)


class BaseFrameToolbar(QFrame):
    """Base class using QFrame for maximum layout control.
    
    Provides:
    - Full layout control via QHBoxLayout
    - Integration with ToolbarLayoutManager
    - Consistent styling via QSS
    - Easy customization
    """
    
    def __init__(self, parent=None):
        """Initialize base frame toolbar.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.setObjectName("BaseFrameToolbar")
        
        # Initialize layout manager
        self.layout_manager = ToolbarLayoutManager()
        self.layout = self.layout_manager.setup_horizontal_layout(self)
        
        self._setup_styling()
    
    def _setup_styling(self):
        """Apply base QSS styling."""
        self.setStyleSheet("""
            QFrame#BaseFrameToolbar {
                background-color: var(--color-bg-dark, #1E1E1E);
                border: 1px solid var(--color-border, #333333);
                border-radius: 4px;
                padding: 4px;
            }
        """)
    
    def add_left_button(self, icon_name: str, tooltip: str, 
                       style_variant: str = "default") -> BaseToolbarButton:
        """Add button to left side of toolbar.
        
        Args:
            icon_name: Icon name for the button
            tooltip: Tooltip text
            style_variant: Style variant for the button
            
        Returns:
            Created BaseToolbarButton
        """
        button = BaseToolbarButton(icon_name, tooltip, style_variant)
        self.layout_manager.add_left_group([button])
        return button
    
    def add_right_button(self, icon_name: str, tooltip: str, 
                        style_variant: str = "default") -> BaseToolbarButton:
        """Add button to right side of toolbar.
        
        Args:
            icon_name: Icon name for the button
            tooltip: Tooltip text
            style_variant: Style variant for the button
            
        Returns:
            Created BaseToolbarButton
        """
        button = BaseToolbarButton(icon_name, tooltip, style_variant)
        self.layout_manager.add_right_group([button])
        return button
    
    def add_center_widget(self, widget: QWidget, stretch: int = 1):
        """Add widget to center with stretch.
        
        Args:
            widget: Widget to add
            stretch: Stretch factor
        """
        self.layout_manager.add_center_group([widget], stretch=stretch)
    
    def add_separator(self):
        """Add visual separator."""
        self.layout_manager.add_separator()


class TableViewBaseToolbar(BaseFrameToolbar):
    """Specialized base class for table view toolbars.
    
    Implements the standard table view toolbar pattern with:
    - Column visibility control
    - Search functionality  
    - Export capabilities
    - Consistent layout and styling
    """
    
    # Standard signals for table view toolbars
    filter_applied = Signal(object, str)  # column, pattern
    filters_cleared = Signal()
    column_visibility_requested = Signal()
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize table view base toolbar."""
        super().__init__(parent)
        self.setObjectName("TableViewBaseToolbar")

        # Clear the existing layout from BaseFrameToolbar
        existing_layout = super().layout()
        if existing_layout:
            existing_layout.deleteLater()

        # Use specialized table view layout
        self.table_layout = TableViewToolbarLayout()
        self.layout = self.table_layout.setup_layout(self)
        
        # Initialize components
        self._init_components()
        self._connect_signals()
    
    def _init_components(self):
        """Initialize standard table view toolbar components."""
        # Column visibility button (left) - use nav icon instead
        self.column_button = self._create_nav_icon_button(
            "view_data",  # Use the nav icon for view_data (eye icon)
            "Show/Hide Columns",
            "default"
        )
        self.table_layout.add_column_button(self.column_button)
        
        # Search group (center)
        self._init_search_group()
        
        # Export button (right) - use existing toolbar export icon
        self.export_button = BaseToolbarButton(
            icon_name="export",  # This should exist in toolbar category
            tooltip="Export Data",
            style_variant="default"
        )
        self.table_layout.add_export_button(self.export_button)
    
    def _init_search_group(self):
        """Initialize the search group components."""
        # Search input (expandable)
        from PySide6.QtWidgets import QLineEdit
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search...")
        self.search_input.setObjectName("ToolbarSearchInput")
        
        # "in:" label
        self.in_label = QLabel("in:")
        self.in_label.setObjectName("ToolbarInLabel")
        
        # Column dropdown (shrink to fit)
        self.column_dropdown = QComboBox()
        self.column_dropdown.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.column_dropdown.setObjectName("ToolbarColumnDropdown")
        
        # Add to layout
        self.table_layout.add_search_group(
            self.search_input, 
            self.column_dropdown, 
            self.in_label
        )
    
    def _connect_signals(self):
        """Connect component signals to toolbar signals."""
        # Column button
        self.column_button.clicked.connect(self.column_visibility_requested.emit)
        
        # Export button  
        self.export_button.clicked.connect(self.csv_export_requested.emit)
        
        # Search input - to be connected by subclasses
        # self.search_input.textChanged.connect(...)
    
    def set_columns(self, columns: List[str], column_names: Optional[Dict[str, str]] = None):
        """Set available columns for the toolbar.
        
        Args:
            columns: List of column identifiers
            column_names: Optional mapping of column IDs to display names
        """
        # Filter out internal columns
        filtered_columns = self._filter_user_columns(columns)
        
        # Update column dropdown
        self.column_dropdown.clear()
        for col in filtered_columns:
            display_name = column_names.get(col, col) if column_names else col
            self.column_dropdown.addItem(display_name, col)
    
    def _filter_user_columns(self, columns: List[str]) -> List[str]:
        """Filter out internal database columns from user visibility.
        
        Args:
            columns: Original column list
            
        Returns:
            Filtered column list
        """
        internal_columns = {'db_uid', 'source_uid', 'is_deleted'}
        return [col for col in columns if col.lower() not in internal_columns]
    
    def get_current_search_column(self) -> Optional[str]:
        """Get currently selected search column.
        
        Returns:
            Column identifier or None
        """
        return self.column_dropdown.currentData()
    
    def get_search_text(self) -> str:
        """Get current search text.
        
        Returns:
            Search text
        """
        return self.search_input.text()
    
    def clear_search(self):
        """Clear the search input."""
        self.search_input.clear()
        self.filters_cleared.emit()

    def _create_nav_icon_button(self, icon_name: str, tooltip: str,
                               style_variant: str = "default") -> BaseToolbarButton:
        """Create a button using a navigation icon.

        Args:
            icon_name: Navigation icon name (e.g., "view_data")
            tooltip: Tooltip text
            style_variant: Style variant

        Returns:
            BaseToolbarButton with nav icon
        """
        from PySide6.QtCore import QSize
        from fm.gui.icons.icon_manager import icon_manager
        from fm.gui.icons.icon_renderer import IconRenderer

        button = BaseToolbarButton(tooltip=tooltip, style_variant=style_variant)

        try:
            # Load nav icon
            icon_path = icon_manager.get_nav_icon(icon_name)
            icon = IconRenderer.load_icon(icon_path, QSize(16, 16))
            button.setIcon(icon)
            button.setIconSize(QSize(16, 16))
        except Exception as e:
            print(f"Warning: Could not load nav icon '{icon_name}': {e}")

        return button
