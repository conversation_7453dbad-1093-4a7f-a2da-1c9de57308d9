"""
Test Script for Table View Toolbar V3

Simple test to verify the new toolbar works correctly before switching over.
Run this to test the toolbar in isolation.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parents[7]  # Adjust as needed
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

from table_view_toolbar_v3 import TableViewToolbarV3


class ToolbarTestWindow(QMainWindow):
    """Simple test window for the V3 toolbar."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Table View Toolbar V3 Test")
        self.setGeometry(100, 100, 800, 200)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Add title
        title = QLabel("Table View Toolbar V3 Test")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Create and add the toolbar
        self.toolbar = TableViewToolbarV3()
        layout.addWidget(self.toolbar)
        
        # Add some test columns
        test_columns = [
            'id', 'date', 'details', 'amount', 'balance', 'account',
            'db_uid', 'source_uid', 'is_deleted'  # These should be filtered out
        ]
        
        test_column_names = {
            'id': 'ID',
            'date': 'Date',
            'details': 'Details', 
            'amount': 'Amount',
            'balance': 'Balance',
            'account': 'Account',
            'db_uid': 'Database UID',
            'source_uid': 'Source UID',
            'is_deleted': 'Is Deleted'
        }
        
        self.toolbar.set_columns(test_columns, test_column_names)
        
        # Connect signals for testing
        self._connect_test_signals()
        
        # Add status label
        self.status_label = QLabel("Ready - Try searching or clicking buttons")
        self.status_label.setStyleSheet("margin: 10px; padding: 5px; background-color: #2D2D2D; border-radius: 3px;")
        layout.addWidget(self.status_label)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        # Apply dark theme styling
        self._apply_dark_theme()
    
    def _connect_test_signals(self):
        """Connect toolbar signals for testing."""
        self.toolbar.filter_applied.connect(self._on_filter_applied)
        self.toolbar.filters_cleared.connect(self._on_filters_cleared)
        self.toolbar.column_visibility_requested.connect(self._on_column_visibility_requested)
        self.toolbar.csv_export_requested.connect(self._on_csv_export_requested)
    
    def _on_filter_applied(self, column, pattern):
        """Handle filter applied signal."""
        self.status_label.setText(f"Filter Applied: '{pattern}' in column '{column}'")
        print(f"DEBUG: Filter applied - Column: {column}, Pattern: {pattern}")
    
    def _on_filters_cleared(self):
        """Handle filters cleared signal."""
        self.status_label.setText("Filters Cleared")
        print("DEBUG: Filters cleared")
    
    def _on_column_visibility_requested(self):
        """Handle column visibility request."""
        self.status_label.setText("Column Visibility Requested")
        print("DEBUG: Column visibility requested")
    
    def _on_csv_export_requested(self):
        """Handle CSV export request."""
        self.status_label.setText("CSV Export Requested")
        print("DEBUG: CSV export requested")
    
    def _apply_dark_theme(self):
        """Apply a simple dark theme for testing."""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1E1E1E;
                color: #FFFFFF;
            }
            
            QLabel {
                color: #FFFFFF;
            }
            
            /* Define CSS variables for the toolbar */
            * {
                --color-bg-dark: #1E1E1E;
                --color-border: #333333;
                --color-primary: #3B8A45;
                --color-text-primary: #FFFFFF;
                --color-text-secondary: #CCCCCC;
            }
        """)


def main():
    """Run the toolbar test."""
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show test window
    window = ToolbarTestWindow()
    window.show()
    
    print("Toolbar V3 Test Started")
    print("=" * 50)
    print("Test Instructions:")
    print("1. Check that layout shows: [Eye] [Search Box] 'in:' [Dropdown] [Export]")
    print("2. Verify internal columns (db_uid, source_uid, is_deleted) are NOT in dropdown")
    print("3. Try typing in search box - should see filter applied messages")
    print("4. Try changing the column dropdown")
    print("5. Click the eye and export buttons")
    print("=" * 50)
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
