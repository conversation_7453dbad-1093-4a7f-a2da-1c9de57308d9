"""
Test IntegratedTextButton Implementation

This script tests the new IntegratedTextButton class and validates
different approaches for embedding buttons in text fields.
"""

import sys
from pathlib import Path

# Add the flatmate source to path for testing
sys.path.insert(0, str(Path(__file__).parent / "flatmate" / "src"))

from PySide6.QtWidgets import (QApplication, QLineEdit, QVBoxLayout, QWidget, 
                               QLabel, QHBoxLayout)
from PySide6.QtCore import QSize

try:
    from fm.gui._shared_components.toolbar import (
        IntegratedTextButton, 
        IntegratedTextFieldAction,
        create_integrated_apply_button,
        create_integrated_clear_button,
        create_integrated_search_icon
    )
    TOOLBAR_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import toolbar components: {e}")
    TOOLBAR_AVAILABLE = False


class IntegratedTextButtonTest(QWidget):
    """Test widget for IntegratedTextButton functionality."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IntegratedTextButton Test")
        self.setGeometry(100, 100, 600, 500)
        
        layout = QVBoxLayout(self)
        
        # Results display
        self.results_label = QLabel("Testing IntegratedTextButton implementations...")
        layout.addWidget(self.results_label)
        
        if TOOLBAR_AVAILABLE:
            self.test_integrated_buttons()
            self.test_addaction_approach()
            self.test_composite_approach()
        else:
            layout.addWidget(QLabel("❌ Toolbar components not available - check imports"))
        
        layout.addWidget(self.results_label)
    
    def test_integrated_buttons(self):
        """Test standalone IntegratedTextButton components."""
        layout = self.layout()
        
        layout.addWidget(QLabel("Test 1: Standalone IntegratedTextButton Components"))
        
        # Test different button variants
        button_layout = QHBoxLayout()
        
        # Apply button (20px)
        apply_btn = create_integrated_apply_button()
        apply_btn.clicked.connect(lambda: self.log_result("✅ Apply button clicked (20px)"))
        button_layout.addWidget(QLabel("Apply:"))
        button_layout.addWidget(apply_btn)
        
        # Clear button (18px)
        clear_btn = create_integrated_clear_button()
        clear_btn.clicked.connect(lambda: self.log_result("✅ Clear button clicked (18px)"))
        button_layout.addWidget(QLabel("Clear:"))
        button_layout.addWidget(clear_btn)
        
        # Search icon (18px, disabled)
        search_icon = create_integrated_search_icon()
        button_layout.addWidget(QLabel("Search:"))
        button_layout.addWidget(search_icon)
        
        button_layout.addStretch()
        
        button_widget = QWidget()
        button_widget.setLayout(button_layout)
        layout.addWidget(button_widget)
        
        self.log_result("✅ IntegratedTextButton components created successfully")
    
    def test_addaction_approach(self):
        """Test QLineEdit.addAction() with IntegratedTextFieldAction."""
        layout = self.layout()
        
        layout.addWidget(QLabel("Test 2: QLineEdit.addAction() Integration"))
        
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("Type here... (integrated apply button)")
        
        # Add search icon (leading)
        search_action = IntegratedTextFieldAction.create_search_action(line_edit)
        if search_action:
            self.log_result("✅ Search action added to leading position")
        
        # Add apply button (trailing)
        apply_action = IntegratedTextFieldAction.create_apply_action(
            line_edit, 
            lambda: self.log_result("✅ Integrated apply action clicked")
        )
        if apply_action:
            self.log_result("✅ Apply action added to trailing position")
        
        layout.addWidget(line_edit)
    
    def test_composite_approach(self):
        """Test composite widget with IntegratedTextButton."""
        layout = self.layout()
        
        layout.addWidget(QLabel("Test 3: Composite Widget with IntegratedTextButton"))
        
        # Create composite widget
        composite = QWidget()
        composite_layout = QHBoxLayout(composite)
        composite_layout.setContentsMargins(4, 4, 4, 4)  # Rational margins
        composite_layout.setSpacing(4)  # Rational spacing between elements
        
        # Style container to look like text field
        composite.setStyleSheet("""
            QWidget {
                border: 1px solid #333333;
                border-radius: 4px;
                background-color: #1E1E1E;
            }
            QWidget:focus-within {
                border-color: #3B8A45;
            }
        """)
        
        # Search icon (decorative)
        search_icon = create_integrated_search_icon()
        composite_layout.addWidget(search_icon)
        
        # Line edit (no border, proper padding)
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("Composite approach with integrated buttons...")
        line_edit.setStyleSheet("""
            QLineEdit {
                border: none;
                background: transparent;
                color: white;
                padding: 6px 8px;  /* Rational padding for text alignment */
                margin: 0px;       /* No margin to align with buttons */
            }
        """)
        composite_layout.addWidget(line_edit, 1)  # Expandable

        # Apply button (integrated size) - positioned rationally, not hard right
        apply_btn = create_integrated_apply_button()
        apply_btn.clicked.connect(lambda: self.log_result("✅ Composite apply button clicked"))
        composite_layout.addWidget(apply_btn)  # Natural positioning with spacing
        
        layout.addWidget(composite)
        
        # External clear button for comparison
        external_layout = QHBoxLayout()
        external_layout.addWidget(composite, 1)
        
        clear_btn = create_integrated_clear_button()
        clear_btn.clicked.connect(lambda: self.log_result("✅ External clear button clicked"))
        external_layout.addWidget(clear_btn)
        
        external_widget = QWidget()
        external_widget.setLayout(external_layout)
        layout.addWidget(external_widget)
        
        self.log_result("✅ Composite widget approach implemented")
    
    def log_result(self, message):
        """Log test results."""
        current_text = self.results_label.text()
        if "Testing IntegratedTextButton" in current_text:
            self.results_label.setText(message)
        else:
            self.results_label.setText(current_text + "\n" + message)
        print(f"TEST: {message}")


def main():
    """Run the test application."""
    app = QApplication(sys.argv)
    
    # Set dark theme for testing
    app.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: white;
        }
        QLineEdit {
            background-color: #1E1E1E;
            border: 1px solid #333333;
            border-radius: 4px;
            padding: 4px;
            color: white;
        }
        QLabel {
            color: white;
            font-weight: bold;
            margin: 10px 0 5px 0;
        }
    """)
    
    test_widget = IntegratedTextButtonTest()
    test_widget.show()
    
    print("=== IntegratedTextButton Test ===")
    print("Testing specialized buttons for text field integration...")
    print("Click the buttons to verify functionality and sizing.")
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
