[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://github.com/quinnjin78/flatmate.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "origin/Continuing_events_nav_dev_with_cascade_base"]
	vscode-merge-base = origin/main
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/origin/Continuing_events_nav_dev_with_cascade_base
[branch "fmTransWrangler"]
	vscode-merge-base = origin/origin/Continuing_events_nav_dev_with_cascade_base
	remote = origin
	merge = refs/heads/fmTransWrangler
[branch "TransCat_MainWindow-Refactor"]
	vscode-merge-base = origin/fmTransWrangler
	remote = origin
	merge = refs/heads/TransCat_MainWindow-Refactor
[branch "Refactor-statement-handler-system"]
	vscode-merge-base = origin/TransCat_MainWindow-Refactor
	remote = origin
	merge = refs/heads/Refactor-statement-handler-system
[branch "refactoring-statement-handlers"]
	vscode-merge-base = origin/Refactor-statement-handler-system
	remote = origin
	merge = refs/heads/refactoring-statement-handlers
[branch "cat-transactions_mvp-for-personal-use"]
	vscode-merge-base = origin/refactoring-statement-handlers
	remote = origin
	merge = refs/heads/cat-transactions_mvp-for-personal-use
[branch "refactor-Tview-tool-bar"]
	vscode-merge-base = origin/cat-transactions_mvp-for-personal-use
	remote = origin
	merge = refs/heads/refactor-Tview-tool-bar
