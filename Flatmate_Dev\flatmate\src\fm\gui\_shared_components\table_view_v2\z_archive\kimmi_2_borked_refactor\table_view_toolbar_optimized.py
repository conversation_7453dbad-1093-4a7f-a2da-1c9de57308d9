"""
Table View Toolbar - Optimized Implementation with New Layout

Complete toolbar implementation with the new layout specification:
visible_columns_selector [left] > Textbox [expand_h] > "in:" label > search_col_selector > export_button [right]
"""

from typing import Dict, List, Optional, Any
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFrame, QLabel

from .toolbar_manager import ToolbarManager
from .toolbar_factory import ToolbarFactory
from .layout_manager import ToolbarLayoutManager
from .groups import FilterGroup, ColumnGroup, ExportGroup


class TableViewToolbarOptimized(QFrame):
    """Optimized toolbar with new layout specification."""
    
    # Signals for external communication
    filter_applied = Signal(object, str)  # column, pattern
    filters_cleared = Signal()
    column_visibility_requested = Signal()
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the optimized toolbar with new layout."""
        super().__init__(parent)
        self.setObjectName("TableViewToolbarOptimized")
        
        # Initialize core components
        self._toolbar_manager = ToolbarManager()
        self._toolbar_factory = ToolbarFactory()
        self._layout_manager = ToolbarLayoutManager()
        
        # Initialize UI
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI with new layout specification."""
        layout = self._layout_manager.setup_layout(self)
        
        # Create components using factory
        self.visible_columns_selector = self._toolbar_factory.create_column_group()
        self.filter_group = self._toolbar_factory.create_filter_group()
        self.export_group = self._toolbar_factory.create_export_group()
        
        # Add components to layout
        self._layout_manager.add_visible_columns_selector(self.visible_columns_selector)
        self._layout_manager.add_search_textbox(self.filter_group.search_textbox)
        self._layout_manager.add_in_label()
        self._layout_manager.add_search_column_selector(self.filter_group.search_column_selector)
        self._layout_manager.add_export_button(self.export_group.export_button)
    
    def _connect_signals(self):
        """Connect all signals for the optimized architecture."""
        # Connect filter signals
        self.filter_group.filter_applied.connect(self.filter_applied)
        self.filter_group.filters_cleared.connect(self.filters_cleared)
        
        # Connect column signals
        self.visible_columns_selector.column_visibility_requested.connect(
            self.column_visibility_requested)
        
        # Connect export signals
        self.export_group.csv_export_requested.connect(self.csv_export_requested)
        self.export_group.excel_export_requested.connect(self.excel_export_requested)
    
    def set_columns(self, columns: List[str], column_names: Optional[Dict[str, str]] = None):
        """Set available columns for the toolbar."""
        self.filter_group.set_columns(columns, column_names)
        self.visible_columns_selector.set_columns(columns, column_names)
    
    def get_state(self) -> Dict[str, Any]:
        """Get current toolbar state for persistence."""
        return {
            'filter_state': self.filter_group.get_state(),
            'column_state': self.visible_columns_selector.get_state(),
            'export_state': self.export_group.get_state()
        }
    
    def set_state(self, state: Dict[str, Any]):
        """Restore toolbar state from persistence."""
        self.filter_group.set_state(state.get('filter_state', {}))
        self.visible_columns_selector.set_state(state.get('column_state', {}))
        self.export_group.set_state(state.get('export_state', {}))