"""
Test hybrid optimization for simple vs complex query handling.
"""

from ..search_query_parser import SearchQueryParser
import time


def test_query_classification():
    """Test the simple vs complex query classification."""
    print("Testing Query Classification")
    print("=" * 40)
    
    parser = SearchQueryParser()
    
    test_cases = [
        # (query, expected_simple, description)
        ("coffee", True, "Single word"),
        ("coffee shop", True, "Space-separated AND"),
        ("coffee -decaf", True, "Word with exclusion"),
        ("restaurant lunch -dinner", True, "Multiple words with exclusion"),
        ("", True, "Empty query"),
        ("  coffee  ", True, "Word with whitespace"),
        
        # Complex queries (should use package parser)
        ("coffee OR tea", False, "OR operator"),
        ("coffee|tea", False, "Pipe OR"),
        ("coffee/tea", False, "Slash OR"),
        ("(coffee OR tea)", False, "Parentheses"),
        ('"coffee shop"', False, "Quoted phrase"),
        ("coffee AND tea", False, "Explicit AND"),
        ("NOT decaf", False, "Explicit NOT"),
        ("((coffee OR tea) AND hot)", False, "Complex nesting"),
    ]
    
    for query, expected_simple, description in test_cases:
        is_simple = parser._is_simple_query(query)
        status = "✓" if is_simple == expected_simple else "✗"
        route = "Simple" if is_simple else "Complex"
        print(f"  {status} {description:20} | '{query}' → {route}")
        
        if is_simple != expected_simple:
            print(f"    Expected: {'Simple' if expected_simple else 'Complex'}")


def test_simple_query_evaluation():
    """Test the simple query evaluation logic."""
    print("\nTesting Simple Query Evaluation")
    print("=" * 40)
    
    parser = SearchQueryParser()
    
    test_data = [
        "Starbucks coffee shop purchase",
        "Tea house visit",
        "Decaf coffee from cafe",
        "Regular coffee and tea",
        "Gas station snacks",
    ]
    
    test_cases = [
        # (query, expected_matches, description)
        ("coffee", [0, 2, 3], "Single word match"),
        ("coffee shop", [0], "AND logic"),
        ("coffee -decaf", [0, 3], "Exclusion logic"),
        ("tea", [1, 3], "Different word"),
        ("gas", [4], "Another word"),
        ("nonexistent", [], "No matches"),
        ("", [0, 1, 2, 3, 4], "Empty query matches all"),
    ]
    
    for query, expected_indices, description in test_cases:
        matches = []
        for i, data in enumerate(test_data):
            if parser._evaluate_simple_query(query, data):
                matches.append(i)
        
        success = matches == expected_indices
        status = "✓" if success else "✗"
        print(f"  {status} {description:20} | '{query}' → {len(matches)} matches")
        
        if not success:
            print(f"    Expected: {expected_indices}")
            print(f"    Got:      {matches}")


def test_hybrid_performance():
    """Test performance comparison between simple and complex routing."""
    print("\nTesting Hybrid Performance")
    print("=" * 40)
    
    parser = SearchQueryParser()
    
    # Test data
    test_data = "Starbucks coffee shop with tea and regular coffee"
    
    # Simple queries
    simple_queries = [
        "coffee",
        "coffee shop",
        "coffee -decaf",
        "tea coffee",
        "starbucks regular"
    ]
    
    # Complex queries  
    complex_queries = [
        "coffee OR tea",
        "(coffee OR tea) -decaf",
        '"coffee shop"',
        "coffee AND tea",
        "NOT decaf"
    ]
    
    # Benchmark simple queries
    print("\nSimple Query Performance:")
    for query in simple_queries:
        # Test with hybrid optimization
        start = time.perf_counter()
        for _ in range(1000):
            parser.evaluate(query, test_data)
        end = time.perf_counter()
        hybrid_time = (end - start) * 1000 / 1000
        
        # Test with package parser (disable hybrid)
        parser._use_hybrid_optimization = False
        start = time.perf_counter()
        for _ in range(1000):
            parser.evaluate(query, test_data)
        end = time.perf_counter()
        package_time = (end - start) * 1000 / 1000
        parser._use_hybrid_optimization = True
        
        improvement = ((package_time - hybrid_time) / package_time * 100) if package_time > 0 else 0
        
        print(f"  '{query}':")
        print(f"    Hybrid:  {hybrid_time:.3f}ms")
        print(f"    Package: {package_time:.3f}ms")
        print(f"    Improvement: {improvement:+.1f}%")
    
    # Benchmark complex queries (should use package parser anyway)
    print("\nComplex Query Performance:")
    for query in complex_queries:
        start = time.perf_counter()
        for _ in range(1000):
            try:
                parser.evaluate(query, test_data)
            except:
                pass
        end = time.perf_counter()
        time_ms = (end - start) * 1000 / 1000
        
        route = "Simple" if parser._is_simple_query(query) else "Package"
        print(f"  '{query}' → {route}: {time_ms:.3f}ms")


def test_correctness_comparison():
    """Test that hybrid optimization produces same results as package parser."""
    print("\nTesting Correctness Comparison")
    print("=" * 40)
    
    parser = SearchQueryParser()
    
    test_data = [
        "Starbucks coffee shop purchase",
        "Tea house visit", 
        "Decaf coffee from cafe",
        "Regular coffee and tea",
        "Gas station snacks",
    ]
    
    # Test queries that should work with both approaches
    test_queries = [
        "coffee",
        "coffee shop", 
        "coffee -decaf",
        "tea coffee",
        "gas",
        "regular coffee",
        "house visit",
    ]
    
    mismatches = 0
    
    for query in test_queries:
        # Get results with hybrid optimization
        parser._use_hybrid_optimization = True
        hybrid_results = []
        for data in test_data:
            hybrid_results.append(parser.evaluate(query, data))
        
        # Get results with package parser only
        parser._use_hybrid_optimization = False
        package_results = []
        for data in test_data:
            package_results.append(parser.evaluate(query, data))
        
        # Compare results
        if hybrid_results == package_results:
            print(f"  ✓ '{query}' - Results match")
        else:
            print(f"  ✗ '{query}' - Results differ!")
            print(f"    Hybrid:  {hybrid_results}")
            print(f"    Package: {package_results}")
            mismatches += 1
        
        # Restore hybrid optimization
        parser._use_hybrid_optimization = True
    
    print(f"\nCorrectness Summary: {len(test_queries) - mismatches}/{len(test_queries)} queries match")
    
    if mismatches == 0:
        print("🎉 Perfect correctness! Hybrid optimization maintains accuracy.")
    else:
        print(f"⚠️  {mismatches} mismatches found. Review implementation.")


def main():
    """Run all hybrid optimization tests."""
    print("Hybrid Optimization Test Suite")
    print("=" * 50)
    
    test_query_classification()
    test_simple_query_evaluation()
    test_hybrid_performance()
    test_correctness_comparison()
    
    print("\nHybrid optimization testing completed!")


if __name__ == "__main__":
    main()
