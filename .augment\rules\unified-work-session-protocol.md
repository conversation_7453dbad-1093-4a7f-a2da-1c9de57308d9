---
description: Unified protocol for all work sessions - features, refactoring, troubleshooting
type: "core_workflow"
---

# Unified Work Session Protocol

**For**: One-person AI-assisted development  
**Covers**: Feature development, refactoring, troubleshooting, bug fixes  
**Version**: 1.0  
**Date**: 2025-01-20

---

## Session Types & Documentation Structure

### Work Session Classification
1. **FEATURE** - New functionality (follows feature-protocol_v1.1.md)
2. **REFACTOR** - Code improvement, architecture changes
3. **TROUBLESHOOT** - Bug fixes, issue resolution
4. **MAINTENANCE** - Updates, cleanup, technical debt

### Universal Documentation Structure
```
flatmate/DOCS/_FEATURES/<session_name>/
├── SESSION_LOG.md           # MANDATORY - Real-time session log
├── CHANGELOG.md            # MANDATORY - Final session summary
├── _REQUIREMENTS_prd.md    # For FEATURE sessions
├── DESIGN.md              # For FEATURE sessions  
├── TASKS.md               # For FEATURE sessions
├── TROUBLESHOOT_LOG.md    # For TROUBLESHOOT sessions
├── REFACTOR_PLAN.md       # For REFACTOR sessions
└── EVIDENCE/              # Supporting files, screenshots, logs
    ├── error_logs/
    ├── screenshots/
    └── code_samples/
```

---

## Universal Session Workflow

### PHASE 1: Session Setup (MANDATORY)
**Time**: 2-3 minutes  
**AI Actions:**
1. **Classify Session Type**: FEATURE | REFACTOR | TROUBLESHOOT | MAINTENANCE
2. **Create Session Folder**: `flatmate/DOCS/_FEATURES/<session_name>/`
3. **Create SESSION_LOG.md**: Real-time documentation
4. **Set Working Context**: What are we trying to accomplish?

**Session Naming Convention:**
- `FEATURE_<name>` (e.g., `FEATURE_search_builder`)
- `REFACTOR_<component>` (e.g., `REFACTOR_toolbar_architecture`)
- `TROUBLESHOOT_<issue>` (e.g., `TROUBLESHOOT_column_filtering`)
- `MAINTENANCE_<area>` (e.g., `MAINTENANCE_dependency_updates`)

### PHASE 2: Session Execution (Type-Specific)

#### For FEATURE Sessions
**Follow**: `feature-protocol_v1.1.md`  
**Document In**: SESSION_LOG.md (real-time) + standard feature docs

#### For REFACTOR Sessions
**Process:**
1. **Document Current State** - What exists now?
2. **Define Target State** - What should it become?
3. **Create Refactor Plan** - Step-by-step approach
4. **Execute Changes** - With testing at each step
5. **Verify Results** - Ensure nothing broke

**Document In**: SESSION_LOG.md + REFACTOR_PLAN.md

#### For TROUBLESHOOT Sessions
**Follow Enhanced Troubleshooting Process:**
1. **Clarify Problem** - Actual vs expected behavior
2. **Gather Evidence** - Logs, errors, stack traces
3. **Isolate Issue** - Minimal reproduction
4. **Hypothesize Causes** - Root causes, not symptoms
5. **Test Hypotheses** - Minimal, reversible changes
6. **Implement Solution** - Clean, tested fix
7. **Verify Resolution** - Confirm fix works
8. **Document Lessons** - Prevent recurrence

**Document In**: SESSION_LOG.md + TROUBLESHOOT_LOG.md

#### For MAINTENANCE Sessions
**Process:**
1. **Identify Scope** - What needs maintenance?
2. **Plan Approach** - Order of operations
3. **Execute Tasks** - With verification
4. **Update Documentation** - Reflect changes

**Document In**: SESSION_LOG.md

### PHASE 3: Session Completion (MANDATORY)
**Time**: 5-10 minutes  
**AI Actions:**
1. **Create/Update CHANGELOG.md** - Following update-docs.md protocol
2. **Update Architecture Docs** - If significant changes made
3. **Record Technical Debt** - Any shortcuts or issues identified
4. **Archive Evidence** - Move logs, screenshots to EVIDENCE/
5. **Update Session Status** - Mark as COMPLETE, ON_HOLD, or NEEDS_FOLLOWUP

---

## Real-Time Documentation: SESSION_LOG.md

### Template Structure
```markdown
# Session Log: <Session Name>

**Date**: YYYY-MM-DD  
**Type**: [FEATURE|REFACTOR|TROUBLESHOOT|MAINTENANCE]  
**Status**: [IN_PROGRESS|COMPLETE|ON_HOLD|NEEDS_FOLLOWUP]  
**Duration**: [Start time - End time]

## Objective
[What are we trying to accomplish?]

## Context
[Why is this work needed? What prompted it?]

## Real-Time Log
### [HH:MM] Session Start
- [Action taken]
- [Discovery made]
- [Decision made]

### [HH:MM] [Phase/Step Name]
- [What happened]
- [What was learned]
- [Next action]

### [HH:MM] Issue Encountered
- **Problem**: [Description]
- **Attempted Solution**: [What was tried]
- **Result**: [What happened]
- **Resolution**: [How it was solved]

### [HH:MM] Session End
- **Status**: [Current state]
- **Next Steps**: [What needs to happen next]
- **Blockers**: [Any impediments identified]

## Key Decisions Made
1. **[Decision]**: [Rationale]
2. **[Decision]**: [Rationale]

## Files Modified
- `path/to/file.py` - [What was changed]
- `path/to/other.py` - [What was changed]

## Evidence Collected
- `EVIDENCE/error_logs/session_errors.log`
- `EVIDENCE/screenshots/before_after.png`

## Technical Debt Identified
- [Issue 1]: [Description and recommended solution]
- [Issue 2]: [Description and recommended solution]

## Session Outcome
[Summary of what was accomplished]
```

---

## Session Completion Checklist

### Every Session Must Have:
- [ ] **SESSION_LOG.md** - Real-time documentation
- [ ] **CHANGELOG.md** - Final summary following update-docs protocol
- [ ] **Files Modified** - Complete list with descriptions
- [ ] **Testing Results** - What was verified to work
- [ ] **Technical Debt** - Any issues identified
- [ ] **Next Steps** - Clear actions for future sessions

### Additional Requirements by Type:
#### FEATURE Sessions:
- [ ] All feature-protocol documents present
- [ ] User acceptance criteria met
- [ ] Architecture docs updated if needed

#### REFACTOR Sessions:
- [ ] REFACTOR_PLAN.md with before/after comparison
- [ ] All affected components tested
- [ ] Performance impact assessed

#### TROUBLESHOOT Sessions:
- [ ] TROUBLESHOOT_LOG.md with problem/solution
- [ ] Root cause identified and documented
- [ ] Prevention measures noted

#### MAINTENANCE Sessions:
- [ ] Scope of maintenance documented
- [ ] All updates verified working
- [ ] Dependencies updated if applicable

---

## Integration with Existing Protocols

### Connection Points:
1. **Feature Protocol**: This protocol handles session setup/completion, feature protocol handles feature-specific steps
2. **Update-Docs Protocol**: This protocol ensures it's executed at session completion
3. **Troubleshooting Protocol**: Enhanced version integrated into this workflow
4. **GUI Component Protocol**: Referenced during component-related sessions

### Workflow Triggers:
- **Start of any work session** → Execute Phase 1 (Setup)
- **During work** → Maintain SESSION_LOG.md
- **End of session** → Execute Phase 3 (Completion)
- **Feature completion** → Execute full update-docs protocol

---

## Benefits for One-Person AI-Assisted Development

### Context Preservation:
- **Between Sessions**: SESSION_LOG.md preserves context
- **Between AI Chats**: CHANGELOG.md provides complete history
- **For Future Work**: Clear documentation of decisions and rationale

### Efficiency Gains:
- **No Lost Work**: Everything documented in real-time
- **Quick Context Switching**: Clear session boundaries
- **Reduced Debugging**: Better error tracking and resolution
- **Faster Onboarding**: New AI sessions can quickly understand context

### Quality Assurance:
- **Consistent Documentation**: Same process for all work types
- **Technical Debt Tracking**: Issues identified and tracked
- **Decision Rationale**: Why choices were made
- **Testing Verification**: What was confirmed to work

---

## Success Metrics

### Documentation Quality:
- Every session has complete SESSION_LOG.md and CHANGELOG.md
- Technical debt is identified and tracked
- Decisions are documented with rationale

### Work Efficiency:
- Context switching time minimized
- No work is lost between sessions
- Clear next steps always identified

### Code Quality:
- All changes are tested and verified
- Architecture decisions are documented
- Technical debt is managed proactively

---

**This unified protocol ensures no work is lost, all decisions are documented, and every session contributes to the overall project knowledge base.**
