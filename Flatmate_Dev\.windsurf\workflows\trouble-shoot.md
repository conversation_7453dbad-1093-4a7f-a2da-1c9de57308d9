---
description:  troubleshooting and discussion workflow

---

# Troubleshooting Task Protocol 


## Collaborative Process
- You are working with the product manager and lead dev
- Their comments will be demarked by a '>>'
- This is a .md-mediated process involving discussion at each major step
- At each key step, update or create relevant .md documentation to ensure shared context and traceability.

---

# PROTOCOL

## Stepwise Troubleshooting Flow

1. **Clarify the problem**
    - What is the actual behaviour?
    - What is the expected behaviour?
    - What is the minimal reproducible scenario?
    - Update the relevant issue or discussion .md with the clarified problem and scenario.

2. **Gather evidence**
    - Check logs, error messages, stack traces
    - Identify and analyse affected modules/files/functions

3. **Isolate the issue**
    - Reproduce the problem in isolation if possible
    - Add targeted logging or assertions (do not obscure errors)

4. **Hypothesise causes**
    - List likely root causes (not symptoms)
    - Rule out speculative or unrelated issues

5. **Test hypotheses**
    - Make minimal, reversible changes
    - Run only relevant tests or checks
    - If test fails, return to Step 4 or earlier as appropriate

6. **Propose solutions and discuss**
    - Provide code examples
    - Consider existing architectural structure and known best practice patterns
    - Consider developer friendliness, enumeration, and type hints
    - Give pros and cons for each possible solution
    - Make your recommendations
    - Discuss with lead dev (user)
    - Identify chosen solution
    - Create actionable plan
    - Discuss and refine
    - Update or create a solution proposal .md summarising all options and the chosen plan.

7. **Implement and test**
    - Implement the chosen solution
    - Test thoroughly
    - If fail, revert if necessary and return to Step 6 or earlier as needed

8. **Fix and verify**
    - Apply the fix cleanly
    - Confirm resolution (tests, logs, UI as appropriate)
    - Update the relevant .md with test results and verification notes.

9. **Clean up**
    - Remove any temporary debug code
    - Document the fix and lessons learned if useful
    - Create or update a postmortem/retrospective .md if warranted.

10. **Escalate if unresolved**
    - Summarise findings and next steps for further help

---

*Draft updated 2025-07-17. Refine as needed for team use.*

---

*Draft created 2025-07-17. Refine as needed for team use.*
