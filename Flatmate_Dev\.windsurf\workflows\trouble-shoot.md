---
description: Troubleshooting and discussion workflow - INTEGRATED with unified work session protocol
integration: Uses .augment/rules/unified-work-session-protocol.md for session management
---

# Troubleshooting Task Protocol

**IMPORTANT**: This workflow is now integrated with the **Unified Work Session Protocol**.
Before starting troubleshooting, execute **Phase 1: Session Setup** from the unified protocol.

## Collaborative Process
- You are working with the product manager and lead dev
- Their comments will be demarked by a '>>'
- This is a .md-mediated process involving discussion at each major step
- **NEW**: All work is documented in SESSION_LOG.md and TROUBLESHOOT_LOG.md
- **NEW**: Session ends with CHANGELOG.md creation following update-docs protocol

---

# ENHANCED TROUBLESHOOTING PROTOCOL

## Stepwise Troubleshooting Flow

1. **Clarify the problem**
    - What is the actual behaviour?
    - What is the expected behaviour?
    - What is the minimal reproducible scenario?
    - Update the relevant issue or discussion .md with the clarified problem and scenario.

2. **Gather evidence**
    - Check logs, error messages, stack traces
    - Identify and analyse affected modules/files/functions

3. **Isolate the issue**
    - Reproduce the problem in isolation if possible
    - Add targeted logging or assertions (do not obscure errors)

4. **Hypothesise causes**
    - List likely root causes (not symptoms)
    - Rule out speculative or unrelated issues

5. **Test hypotheses**
    - Make minimal, reversible changes
    - Run only relevant tests or checks
    - If test fails, return to Step 4 or earlier as appropriate

6. **Propose solutions and discuss**
    - Provide code examples
    - Consider existing architectural structure and known best practice patterns
    - Consider developer friendliness, enumeration, and type hints
    - Give pros and cons for each possible solution
    - Make your recommendations
    - Discuss with lead dev (user)
    - Identify chosen solution
    - Create actionable plan
    - Discuss and refine
    - Update or create a solution proposal .md summarising all options and the chosen plan.

7. **Implement and test**
    - Implement the chosen solution
    - Test thoroughly
    - If fail, revert if necessary and return to Step 6 or earlier as needed

8. **Fix and verify**
    - Apply the fix cleanly
    - Confirm resolution (tests, logs, UI as appropriate)
    - Update the relevant .md with test results and verification notes.

9. **Clean up and Document**
    - Remove any temporary debug code
    - **NEW**: Update SESSION_LOG.md with final resolution
    - **NEW**: Create TROUBLESHOOT_LOG.md with problem/solution summary
    - Document the fix and lessons learned in session documentation

10. **Complete Session** (MANDATORY)
    - **Execute Phase 3** of unified-work-session-protocol.md
    - **Create CHANGELOG.md** following update-docs.md protocol
    - **Archive Evidence** - Move logs, screenshots to EVIDENCE/ folder
    - **Update Technical Debt** registry if architectural issues found
    - **Mark Session Complete** with clear next steps

11. **Escalate if unresolved**
    - Summarise findings and next steps for further help
    - **Ensure all documentation is complete** for handoff

---

## Integration Notes

### Session Setup (Before Step 1):
1. **Create session folder**: `flatmate/DOCS/_FEATURES/TROUBLESHOOT_<issue_name>/`
2. **Initialize SESSION_LOG.md**: Real-time documentation
3. **Set context**: What problem are we solving and why?

### During Troubleshooting:
- **Maintain SESSION_LOG.md**: Document each step, discovery, and decision
- **Collect Evidence**: Save logs, screenshots, code samples to EVIDENCE/ folder
- **Track Time**: Note duration of each phase for future estimation

### Session Completion:
- **TROUBLESHOOT_LOG.md**: Structured problem/solution summary
- **CHANGELOG.md**: Complete session summary following documentation protocol
- **Technical Debt**: Record any architectural issues discovered

---

*Updated 2025-01-20: Integrated with unified work session protocol for complete documentation coverage.*
