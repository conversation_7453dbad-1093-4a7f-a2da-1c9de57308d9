"""
Table View Toolbar - Legacy Implementation

This is the ORIGINAL toolbar implementation that was working before the refactoring.
It uses the group-based architecture with FilterGroup, ColumnGroup, and ExportGroup.

This version is preserved for:
- Backward compatibility
- Reference implementation  
- Fallback option if the new toolbar has issues

Usage:
    from .toolbars import TableViewToolbarLegacy
    
    toolbar = TableViewToolbarLegacy()
    toolbar.set_columns(columns, column_names)
    toolbar.filter_applied.connect(your_handler)
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFrame, QHBoxLayout

from .groups import FilterGroup, ColumnGroup, ExportGroup


class TableViewToolbarLegacy(QFrame):
    """Legacy toolbar implementation using the group-based architecture."""
    
    # Signals for external communication
    filter_applied = Signal(object, str)  # column (int or str), pattern
    filters_cleared = Signal()
    column_visibility_requested = Signal()
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the legacy table view toolbar."""
        super().__init__(parent)

        # Set object name for styling
        self.setObjectName("TableViewToolbarLegacy")

        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """Initialize the user interface."""
        # Create main layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(8)

        # Create groups
        self.filter_group = FilterGroup()
        self.column_group = ColumnGroup()
        self.export_group = ExportGroup()

        # Add groups to layout
        layout.addWidget(self.filter_group, 1)  # Filter group gets most space
        layout.addWidget(self.column_group)
        layout.addWidget(self.export_group)

    def _connect_signals(self):
        """Connect group signals to toolbar signals."""
        # Forward filter group signals
        self.filter_group.filter_applied.connect(self.filter_applied)
        self.filter_group.filters_cleared.connect(self.filters_cleared)
        
        # Forward column group signals
        self.column_group.column_visibility_requested.connect(
            self.column_visibility_requested)

        # Forward export group signals
        self.export_group.csv_export_requested.connect(
            self.csv_export_requested)
        self.export_group.excel_export_requested.connect(
            self.excel_export_requested)

    def set_columns(self, columns, column_names=None):
        """Set available columns for filtering and visibility."""
        self.filter_group.set_columns(columns, column_names)
        self.column_group.set_columns(columns, column_names)

    def get_filter_state(self):
        """Get current filter state."""
        return self.filter_group.get_filter_state()

    def set_filter_state(self, column, pattern):
        """Set filter state."""
        self.filter_group.set_filter_state(column, pattern)

    def clear_filters(self):
        """Clear all filters."""
        self.filter_group.clear_filters()

    def get_selected_columns(self):
        """Get currently selected columns."""
        return self.column_group.get_selected_columns()

    def set_selected_columns(self, columns):
        """Set selected columns."""
        self.column_group.set_selected_columns(columns)
