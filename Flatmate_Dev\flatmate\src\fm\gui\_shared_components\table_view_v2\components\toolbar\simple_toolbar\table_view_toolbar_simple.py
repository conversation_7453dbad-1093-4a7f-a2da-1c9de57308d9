"""
Table View Toolbar Simple - Working Implementation

Based on the successful minimal test, this creates a working toolbar
that addresses your actual requirements without the complex base class issues.
"""

from typing import Dict, List, Optional, Any
from PySide6.QtCore import Signal, QTimer, QSize
from PySide6.QtWidgets import QFrame, QHBoxLayout, QPushButton, QLineEdit, QComboBox, QLabel
from PySide6.QtGui import QIcon


class TableViewToolbarSimple(QFrame):
    """Simple, working table view toolbar implementation.
    
    Features:
    - New layout: [Column] [Search] "in:" [Dropdown] [Export]
    - Internal DB columns filtered out
    - Live search with debouncing
    - Drop-in replacement compatibility
    """
    
    # Standard signals for table view toolbars
    filter_applied = Signal(object, str)  # column, pattern
    filters_cleared = Signal()
    column_visibility_requested = Signal()
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the simple toolbar."""
        super().__init__(parent)
        self.setObjectName("TableViewToolbarSimple")
        
        # Search debouncing
        self._search_timer = QTimer()
        self._search_timer.setSingleShot(True)
        self._search_timer.timeout.connect(self._emit_search_filter)
        
        # Initialize UI
        self._init_ui()
        self._connect_signals()
        self._apply_styling()
    
    def _init_ui(self):
        """Initialize the UI with the new layout specification."""
        # Create main layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(8)
        
        # Column visibility button (left)
        self.column_button = QPushButton()
        self.column_button.setFixedSize(32, 32)
        self.column_button.setToolTip("Show/Hide Columns")
        self._load_column_icon()
        layout.addWidget(self.column_button)
        
        # Search input (expandable center)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search...")
        self.search_input.setObjectName("ToolbarSearchInput")
        layout.addWidget(self.search_input, 1)  # stretch=1
        
        # "in:" label
        self.in_label = QLabel("in:")
        self.in_label.setObjectName("ToolbarInLabel")
        layout.addWidget(self.in_label)
        
        # Column dropdown (shrink to fit)
        self.column_dropdown = QComboBox()
        self.column_dropdown.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.column_dropdown.setObjectName("ToolbarColumnDropdown")
        layout.addWidget(self.column_dropdown)
        
        # Export button (right)
        self.export_button = QPushButton()
        self.export_button.setFixedSize(32, 32)
        self.export_button.setToolTip("Export Data")
        self._load_export_icon()
        layout.addWidget(self.export_button)
    
    def _load_column_icon(self):
        """Load the column visibility icon."""
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            
            # Use the nav icon for view_data (eye icon)
            icon_path = icon_manager.get_nav_icon("view_data")
            icon = IconRenderer.load_icon(icon_path, QSize(16, 16))
            self.column_button.setIcon(icon)
            self.column_button.setIconSize(QSize(16, 16))
        except Exception as e:
            print(f"Warning: Could not load column icon: {e}")
            self.column_button.setText("👁")  # Fallback emoji
    
    def _load_export_icon(self):
        """Load the export icon."""
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            
            # Try to load export icon from toolbar category
            icon_path = icon_manager.get_toolbar_icon("export")
            icon = IconRenderer.load_icon(icon_path, QSize(16, 16))
            self.export_button.setIcon(icon)
            self.export_button.setIconSize(QSize(16, 16))
        except Exception as e:
            print(f"Warning: Could not load export icon: {e}")
            self.export_button.setText("📤")  # Fallback emoji
    
    def _connect_signals(self):
        """Connect component signals."""
        # Button signals
        self.column_button.clicked.connect(self.column_visibility_requested.emit)
        self.export_button.clicked.connect(self.csv_export_requested.emit)
        
        # Search signals with debouncing
        self.search_input.textChanged.connect(self._on_search_text_changed)
        self.column_dropdown.currentTextChanged.connect(self._on_search_column_changed)
    
    def _on_search_text_changed(self, text: str):
        """Handle search text changes with smart debouncing.

        Uses the same logic as the existing FilterGroup for optimal performance.
        """
        # For simple queries, do immediate live filtering
        # For complex queries, use debounced operator detection to avoid UI freezes
        if self._is_likely_simple_query(text):
            # Fast path for obviously simple queries
            self._emit_search_filter()
        else:
            # Potentially complex query - use debounced detection
            self._search_timer.stop()
            if text.strip():
                self._search_timer.start(150)  # 150ms debounce for complex queries
            else:
                # Clear immediately
                self._emit_search_filter()
    
    def _on_search_column_changed(self):
        """Handle search column changes."""
        # Re-apply current search to new column
        if self.search_input.text().strip():
            self._emit_search_filter()
    
    def _emit_search_filter(self):
        """Emit the filter signal with current search parameters."""
        column = self.get_current_search_column()
        pattern = self.get_search_text()

        if column is not None:
            self.filter_applied.emit(column, pattern)

        # If pattern is empty, emit clear signal
        if not pattern.strip():
            self.filters_cleared.emit()

    def _is_likely_simple_query(self, text: str) -> bool:
        """Quick check for obviously simple queries to enable fast path.

        Uses the same logic as the existing FilterGroup for consistency.
        """
        if not text or not text.strip():
            return True

        # If it contains any of these characters, it's definitely not simple
        complex_chars = ['|', '/', '(', ')', '"']
        return not any(char in text for char in complex_chars)

    def _has_advanced_operators(self, text: str) -> bool:
        """Check if text contains advanced operators that should disable live filtering.

        Uses the search query parser as the single source of truth for this decision.
        This ensures consistency between the UI behavior and the actual parsing logic.
        """
        if not text or not text.strip():
            return False

        # Import the search parser to use its logic
        try:
            from ...table_utils.search_query_parser import get_search_parser
            parser = get_search_parser()
            # If it's NOT a simple query, then it has advanced operators
            return not parser._is_simple_query(text)
        except ImportError:
            # Fallback to basic detection if parser not available
            return self._fallback_advanced_detection(text)

    def _fallback_advanced_detection(self, text: str) -> bool:
        """Fallback advanced operator detection if parser not available."""
        # Basic detection for common advanced operators
        advanced_patterns = [
            '|', '/', 'OR', 'AND', 'NOT', '(', ')', '"',
            ' - ', ' + ', ' & '
        ]
        text_upper = text.upper()
        return any(pattern in text_upper for pattern in advanced_patterns)
    
    def _apply_styling(self):
        """Apply QSS styling."""
        self.setStyleSheet("""
            QFrame#TableViewToolbarSimple {
                background-color: #1E1E1E;
                border: 1px solid #3B8A45;
                border-radius: 4px;
                padding: 4px;
            }
            
            /* Button styling */
            QPushButton {
                background-color: transparent;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 4px;
            }
            
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-color: #3B8A45;
            }
            
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.2);
            }
            
            /* Search input styling */
            QLineEdit#ToolbarSearchInput {
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 4px 8px;
                background-color: #1E1E1E;
                color: #FFFFFF;
                font-size: 14px;
            }
            
            QLineEdit#ToolbarSearchInput:focus {
                border-color: #3B8A45;
            }
            
            /* Column dropdown styling */
            QComboBox#ToolbarColumnDropdown {
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 4px 8px;
                background-color: #1E1E1E;
                color: #FFFFFF;
                min-width: 80px;
                max-width: 200px;
            }
            
            QComboBox#ToolbarColumnDropdown:hover {
                border-color: #3B8A45;
            }
            
            QComboBox#ToolbarColumnDropdown::drop-down {
                border: none;
                width: 20px;
            }
            
            /* "in:" label styling */
            QLabel#ToolbarInLabel {
                color: #CCCCCC;
                font-size: 12px;
                font-weight: normal;
            }
        """)
    
    # Public API methods for compatibility
    
    def set_columns(self, columns: List[str], column_names: Optional[Dict[str, str]] = None):
        """Set available columns for the toolbar.
        
        Args:
            columns: List of column identifiers
            column_names: Optional mapping of column IDs to display names
        """
        # Filter out internal columns
        filtered_columns = self._filter_user_columns(columns)
        
        # Update column dropdown
        self.column_dropdown.clear()
        
        # Add "All Columns" option if there are multiple columns
        if len(filtered_columns) > 1:
            self.column_dropdown.addItem("All Visible Columns", "all_columns")
        
        # Add individual columns
        for col in filtered_columns:
            display_name = column_names.get(col, col) if column_names else col
            # Make display names more user-friendly
            display_name = self._format_column_display_name(display_name)
            self.column_dropdown.addItem(display_name, col)
        
        # Set default to "Details" if available, otherwise first column
        details_index = self.column_dropdown.findData("details")
        if details_index >= 0:
            self.column_dropdown.setCurrentIndex(details_index)
        elif self.column_dropdown.count() > 0:
            self.column_dropdown.setCurrentIndex(0)
    
    def _filter_user_columns(self, columns: List[str]) -> List[str]:
        """Filter out internal database columns from user visibility."""
        internal_columns = {
            'db_uid', 'source_uid', 'is_deleted', 'created_at', 'updated_at',
            'version', 'hash', 'internal_id', 'system_id'
        }
        
        filtered = []
        for col in columns:
            col_lower = col.lower()
            if (col_lower not in internal_columns and 
                not col.startswith('_') and
                not col.startswith('sys_')):
                filtered.append(col)
        
        return filtered
    
    def _format_column_display_name(self, name: str) -> str:
        """Format column names for better display."""
        if '_' in name:
            words = name.split('_')
            return ' '.join(word.capitalize() for word in words)
        return name.capitalize()
    
    def get_current_search_column(self) -> Optional[str]:
        """Get currently selected search column."""
        return self.column_dropdown.currentData()
    
    def get_search_text(self) -> str:
        """Get current search text."""
        return self.search_input.text()
    
    def clear_search(self):
        """Clear the search input."""
        self.search_input.clear()
        self.filters_cleared.emit()
    
    def get_filter_state(self) -> Dict[str, Any]:
        """Get current filter state for persistence."""
        return {
            'search_text': self.get_search_text(),
            'search_column': self.get_current_search_column(),
            'live_filtering': True
        }
    
    def set_filter_state(self, state: Dict[str, Any]):
        """Restore filter state from persistence."""
        if 'search_text' in state:
            self.search_input.setText(state['search_text'])
        
        if 'search_column' in state:
            column = state['search_column']
            index = self.column_dropdown.findData(column)
            if index >= 0:
                self.column_dropdown.setCurrentIndex(index)


# Convenience function for easy usage
def create_simple_toolbar(parent=None) -> TableViewToolbarSimple:
    """Create a new simple toolbar instance."""
    return TableViewToolbarSimple(parent)
