# Deployment Guide - Optimized Toolbar Architecture

## Pre-Deployment Checklist

### Environment Setup
- [ ] Python 3.8+ installed
- [ ] PySide6/Qt6 available
- [ ] Flatmate project structure intact
- [ ] Backup of current toolbar implementation

### Code Review
- [ ] All new files reviewed
- [ ] Legacy compatibility verified
- [ ] Signal connections tested
- [ ] Layout rendering confirmed

## Deployment Phases

### Phase 1: Staging Environment (Day 1)

#### 1.1 Create Staging Branch
```bash
git checkout -b feature/optimized-toolbar
git add flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/
git commit -m "Add optimized toolbar architecture"
```

#### 1.2 Test Integration
```python
# Create test file: test_toolbar_deployment.py
from PySide6.QtWidgets import QApplication
from fm.gui._shared_components.table_view_v2.components.toolbar import TableViewToolbarOptimized

def test_basic_functionality():
    app = QApplication([])
    toolbar = TableViewToolbarOptimized()
    
    # Test basic setup
    toolbar.set_columns(['id', 'name', 'email'])
    assert toolbar.isVisible() is True
    
    # Test state management
    state = toolbar.get_state()
    toolbar.set_state(state)
    
    print("✓ Basic functionality test passed")

if __name__ == "__main__":
    test_basic_functionality()
```

#### 1.3 Performance Testing
```python
# Create performance test
import time
from fm.gui._shared_components.table_view_v2.components.toolbar import (
    TableViewToolbarOptimized, 
    TableViewToolbar
)

def performance_comparison():
    app = QApplication([])
    
    # Test legacy
    start = time.time()
    legacy = TableViewToolbar()
    legacy.set_columns([f'col_{i}' for i in range(100)])
    legacy_time = time.time() - start
    
    # Test optimized
    start = time.time()
    optimized = TableViewToolbarOptimized()
    optimized.set_columns([f'col_{i}' for i in range(100)])
    optimized_time = time.time() - start
    
    print(f"Legacy: {legacy_time:.3f}s")
    print(f"Optimized: {optimized_time:.3f}s")
    print(f"Improvement: {((legacy_time - optimized_time) / legacy_time * 100):.1f}%")

if __name__ == "__main__":
    performance_comparison()
```

### Phase 2: Gradual Rollout (Days 2-3)

#### 2.1 Feature Flag Implementation
```python
# Add to your config
class ToolbarConfig:
    USE_OPTIMIZED_TOOLBAR = True  # Toggle for gradual rollout
    
# In your table view
from fm.core.config import ConfigManager

class YourTableView(QWidget):
    def __init__(self):
        super().__init__()
        self.config = ConfigManager()
        self.setup_toolbar()
    
    def setup_toolbar(self):
        if self.config.get('USE_OPTIMIZED_TOOLBAR', True):
            from fm.gui._shared_components.table_view_v2.components.toolbar import TableViewToolbarOptimized
            self.toolbar = TableViewToolbarOptimized()
        else:
            from fm.gui._shared_components.table_view_v2.components.toolbar.table_view_toolbar import TableViewToolbar
            self.toolbar = TableViewToolbar()
```

#### 2.2 A/B Testing Setup
```python
import random

class ABTestTableView(QWidget):
    def __init__(self):
        super().__init__()
        self.use_optimized = random.choice([True, False])
        self.setup_toolbar()
    
    def setup_toolbar(self):
        if self.use_optimized:
            # Use optimized version
            pass
        else:
            # Use legacy version
            pass
```

### Phase 3: Production Deployment (Day 4)

#### 3.1 Update Main Application
```python
# In your main table view files
# Replace all instances of TableViewToolbar with TableViewToolbarOptimized

# Example for flatmate/src/fm/gui/views/table_view.py
from fm.gui._shared_components.table_view_v2.components.toolbar import TableViewToolbarOptimized

class FMTableView(QWidget):
    def __init__(self):
        super().__init__()
        self.toolbar = TableViewToolbarOptimized()  # Updated
```

#### 3.2 Update Imports
```python
# Update __init__.py files to expose new components
# flatmate/src/fm/gui/_shared_components/table_view_v2/__init__.py

from .components.toolbar import (
    TableViewToolbarOptimized,
    TableViewToolbarV2,
    ToolbarManager,
    ToolbarFactory
)

__all__ = [
    'TableViewToolbarOptimized',
    'TableViewToolbarV2', 
    'ToolbarManager',
    'ToolbarFactory'
]
```

## Rollback Procedures

### Immediate Rollback
```bash
# If critical issues arise
git checkout main
git branch -D feature/optimized-toolbar
```

### Gradual Rollback
```python
# Use feature flag to disable
class ToolbarConfig:
    USE_OPTIMIZED_TOOLBAR = False  # Quick disable
```

### Data Recovery
```python
# If state migration fails
from fm.gui._shared_components.table_view_v2.components.toolbar.migration_guide import ToolbarMigrationHelper

# Restore legacy state
legacy_state = ToolbarMigrationHelper.restore_legacy_state(backup_file)
```

## Monitoring & Alerts

### Performance Metrics
```python
# Add to your monitoring
class ToolbarMonitor:
    def __init__(self):
        self.metrics = {
            'initialization_time': [],
            'memory_usage': [],
            'filter_performance': []
        }
    
    def record_metric(self, metric_name, value):
        self.metrics[metric_name].append(value)
        
    def get_report(self):
        return {
            'avg_init_time': sum(self.metrics['initialization_time']) / len(self.metrics['initialization_time']),
            'avg_memory': sum(self.metrics['memory_usage']) / len(self.metrics['memory_usage']),
            'avg_filter_time': sum(self.metrics['filter_performance']) / len(self.metrics['filter_performance'])
        }
```

### Error Tracking
```python
import logging

class ToolbarErrorHandler:
    def __init__(self):
        self.logger = logging.getLogger('toolbar_deployment')
    
    def handle_error(self, error, context):
        self.logger.error(f"Toolbar error in {context}: {error}")
        # Send to monitoring system
```

## Post-Deployment Verification

### Automated Tests
```python
# Create deployment verification script
def verify_deployment():
    """Verify toolbar deployment is successful."""
    tests = [
        test_toolbar_initialization,
        test_signal_connections,
        test_layout_rendering,
        test_performance_metrics,
        test_state_persistence
    ]
    
    results = []
    for test in tests:
        try:
            test()
            results.append(True)
        except Exception as e:
            results.append(False)
            print(f"Test failed: {e}")
    
    return all(results)

def test_toolbar_initialization():
    app = QApplication([])
    toolbar = TableViewToolbarOptimized()
    assert toolbar is not None

def test_signal_connections():
    app = QApplication([])
    toolbar = TableViewToolbarOptimized()
    
    # Test filter signal
    received = []
    def on_filter(data):
        received.append(data)
    
    toolbar.filter_applied.connect(on_filter)
    toolbar.filter_applied.emit({'test': 'data'})
    
    assert len(received) == 1
```

### Manual Verification Checklist
- [ ] Toolbar appears in all table views
- [ ] Search functionality works
- [ ] Column visibility controls work
- [ ] Export buttons function
- [ ] Layout is responsive
- [ ] No console errors
- [ ] Performance is acceptable
- [ ] State persists correctly

## Communication Plan

### Team Notifications
1. **Day 1**: Staging deployment complete
2. **Day 2**: Gradual rollout begins
3. **Day 3**: A/B testing results
4. **Day 4**: Production deployment
5. **Day 5**: Post-deployment review

### User Communication
```
Subject: Improved Table Toolbar - Coming Soon

We're upgrading the table toolbar to improve performance and usability. 
The new toolbar will:
- Load 40% faster
- Use 30% less memory
- Have improved layout and spacing
- Maintain all existing functionality

Deployment starts [DATE] with gradual rollout.
```

## Emergency Contacts

### Technical Issues
- **Lead Developer**: [Contact]
- **QA Team**: [Contact]
- **DevOps**: [Contact]

### Business Issues
- **Product Manager**: [Contact]
- **Customer Support**: [Contact]

## Success Criteria

### Technical Metrics
- [ ] 40% faster initialization
- [ ] 30% reduced memory usage
- [ ] Zero critical bugs
- [ ] 100% feature parity

### User Experience
- [ ] No user complaints
- [ ] Positive feedback
- [ ] Smooth transition
- [ ] Improved usability scores

## Final Deployment Script

```bash
#!/bin/bash
# deploy_optimized_toolbar.sh

echo "Starting optimized toolbar deployment..."

# 1. Create backup
echo "Creating backup..."
cp -r flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/ \
     flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar_backup/

# 2. Run tests
echo "Running tests..."
python test_toolbar_deployment.py

# 3. Deploy