# App Test Notes: Categorize Module – Table View Toolbar

## Requirements (as of 2025-07-19 19:54:47)

- [x] **Search tool has a search icon on the left**  
    _Textbox now has search icon._
- [ ] **Clear button should be an "x" icon**  
     
    - On review: "x" button should appear hard right inside the textbox, and the apply button should appear to the right (should use return icon, not tick icon).
   
- [x] **Research integrated icons in PySide6/Qt**  
    _Done._
- ~~[x] Clear button would sit outside aligned right and always be present~~
- [ ] **Text search-related icons may need to be inside a container that expands to fit available space**  
    _Has this been implemented? What does the container currently contain? Textbox still does not expand._
- [ ] **Export button should use the export notes icon, not a download icon**  
    _Still just looks like a doc icon. Should use:_  
    `flatmate/resources/icons/export_notes_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24.svg`
- [ ] **Text-related group should expand to fill available space, prioritising the text entry field**  
    _Still not happening. How should it work?_

---

## Issues Raised

### Architecture & Maintainability
- Repeated difficulty in implementing GUI refinements suggests a potential architecture issue.
- The architecture, folder structure, file naming conventions, and icon management system need to be developer-friendly and maintainable.
- [x] Toolbar button class needs to be created for easy, consistent toolbar button changes (integrated button class).

### Feature Protocol
- Feature implementation protocol might need refinement.  
    _Has this been updated?_

### GUI Refinement Protocol
- A GUI refinement protocol might be worth considering.
- Documents on the GUI system in `DOCS/architecture` may need to be included, checked, and rationalised.  
    _Not yet done._

---

## Post-Refactoring Notes (2025-07-19 19:57:12)

### Current Concerns
- **Does the toolbar have a `layout_manager.py`?**  
    **Research:** No such file exists for the toolbar or in the categorize module.
- **Tasks document needs updating (re: layout).**
- **Current layout:**  
  `search_icon > search_col_select > text_box > col_vis_select > export_button`
- **Suggested layout:**  
  `visible_columns_selector [left] > Textbox [expand_h] > "in:" label > search_col_selector > export_button [right]`
- The 'search col select' dropdown should, if possible, shrink to fit its text contents.


# Search: the boolean search operators like brackets (complex search operators) no longer seem to trigger the apply button - we need to audit the relevant code base ...

## Other
- Table view filter/search toolbar components and utils: consider architectural concerns re app structure and naming conventions.

---

## Unrelated to UI Concerns
- **Current search logic in table_view**  
  _Also a version created in core (not currently used)._  
  **Research:**
    - The main filtering logic is in `fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_filter_proxy_model.py.backup` (and `.py`).
    - There is also a version in core, but it is not currently used.
    - **Decision:** The enhanced filter proxy model in the shared components is the active location for search logic. Confirm and document the canonical location for search/filter logic in the codebase for future maintainability.

---

## Research-Backed Answers

- **Does the toolbar have a `layout_manager.py`?**  
  No.
- **Does the export button use the correct icon?**  
  No. Update to use `flatmate/resources/icons/export_notes_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24.svg`.
- **Does the search text field expand horizontally?**  
  No. There is no evidence of a stretch factor or expanding policy applied to the search bar container. The layout should set stretch factors so the search field expands to fill available space.
- **Where does the search logic live?**  
  The enhanced filter proxy model in the shared components is the active location for search logic. There is an unused version in core.

---

## Additional Notes
- **Qt provides a QToolBar class:**  
  [Qt QToolBar Documentation](https://doc.qt.io/qt-6/qtoolbar.html)  
  Consider creating a base toolbar class using QToolBar for future extensibility and maintainability.

---

**Formatting and actionable suggestions applied. All research-backed answers are now inline or appended.**


Testing notes 
typing OR causs crash 
()